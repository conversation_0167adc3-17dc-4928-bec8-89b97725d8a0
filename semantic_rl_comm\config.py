import torch

def get_config():
    """外部获取全部配置参数的字典（推荐唯一入口）"""
    # TODO: [检查并移除冗余的配置参数，例如 image_height vs img_height, sac_buffer_size vs buffer_size 等，统一命名规范]
    return {
        # 环境参数
        "env_name": 'CartPole-v1',
        "image_height": 84,
        "image_width": 84,
        "frame_stack": 4,
        # 发射端参数
        "cnn_channels": [32, 64, 64],
        "cnn_kernel_sizes": [8, 4, 3],
        "cnn_strides": [4, 2, 1],
        "cnn_paddings": [0, 0, 0],
        "flattened_size": 3136,
        "d_codebook": 256,
        "k": 128,
        "gumbel_tau_start": 2.0,
        "gumbel_tau_end": 0.5,
        "gumbel_tau_anneal_steps": 100000,
        "lam": 1e-3,
        "gamma_enc": 0.99,
        "lr_encoder": 1e-4,
        # 信道参数
        "channel_type": 'AWGN',
        "snr_db_train": 10,
        "snr_db_test": list(range(0, 11, 2)),
        # 接收端参数
        "rnn_type": 'lstm', # 'lstm' or 'gru'
        "h_rnn": 512,
        "lr_belief_estimator": 1e-4,
        # SAC参数
        "sac_gamma": 0.99,
        "sac_learning_rate": 3e-4,
        "sac_buffer_size": 1000000,
        "sac_batch_size": 256,
        "sac_tau": 0.005,
        "sac_ent_coef": 'auto',
        "sac_use_sde": False,
        "sac_sde_sample_freq": -1,
        # 训练参数
        "total_timesteps": 2000000,  # 原为total_train_steps，统一与main.py用法
        "log_interval": 1000,
        "model_save_freq": 50000,
        "eval_freq": 25000,
        "num_eval_episodes": 5,
        # 设备与路径
        "device": 'cuda' if torch.cuda.is_available() else 'cpu',
        "log_dir": './semantic_rl_comm/logs/',
        "model_dir": './semantic_rl_comm/models/',
        # 兼容main.py所需的参数补充
        "img_width": 84,
        "img_height": 84,
        "embedding_dim_D": 256,
        "num_embeddings_K": 128,
        "gumbel_tau": 2.0,
        "cnn_feature_dim": 3136,
        "buffer_size": 10000,  # 原为1000000，防止内存溢出
        "batch_size_sac": 256,
        "gamma_sac": 0.99,
        "tau_sac": 0.005,
        "lr_sac_actor": 3e-4,
        "learning_starts": 1000,
        "sac_features_dim": 128,
        "rnn_hidden_dim": 512,
        "num_rnn_layers": 1,
        "seed": 0,
    }

if __name__ == '__main__':
    config = get_config()
    print(f"Device: {config['device']}")
    print(f"Environment: {config['env_name']}")
    print(f"SAC Learning Rate: {config['sac_learning_rate']}")
    print(f"Encoder Learning Rate: {config['lr_encoder']}")
    print(f"Log directory: {config['log_dir']}")