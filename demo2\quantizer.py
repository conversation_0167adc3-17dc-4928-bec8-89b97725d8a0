import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F


class GumbelSoftmaxQuantizer(nn.Module):
    def __init__(self, num_embeddings, embedding_dim, gumbel_tau=1.0):
        super().__init__()
        self.num_embeddings = num_embeddings  # K
        self.embedding_dim = embedding_dim  # D
        self.gumbel_tau = gumbel_tau

        self.codebook = nn.Parameter(torch.Tensor(num_embeddings, embedding_dim))
        nn.init.uniform_(self.codebook, -1 / num_embeddings, 1 / num_embeddings)

    def compute_score(self, X_feat: torch.Tensor, codebook_mask=None):
        # X_feat shape: (B, input_feature_dim)
        scores = torch.matmul(X_feat, self.codebook.transpose(1, 0)) / np.sqrt(self.embedding_dim)

        if codebook_mask is not None:
            return scores[:, codebook_mask]
        else:
            return scores

    def _mod_channel_demod(self, qam_modem_instance, indices: torch.Tensor) -> torch.Tensor:
        """
        Helper to pass indices through QAM modulation, AWGN channel, and demodulation.
        Args:
            qam_modem_instance: An instance of the QAM class.
            indices (torch.Tensor): Batch of integer indices (from codebook). Shape (B,).
        Returns:
            torch.Tensor: Batch of demodulated integer indices. Shape (B,).
        """
        if qam_modem_instance is None:
            return indices  # No channel effect

        # Ensure indices are on the same device as QAM might create tensors
        # (though current QAM creates them on input device or default)
        current_device = self.codebook.device
        indices_on_device = indices.to(current_device)

        modulated_symbols = qam_modem_instance.modulate(indices_on_device)
        noisy_symbols = qam_modem_instance.awgn(modulated_symbols)
        demodulated_indices = qam_modem_instance.demodulate(noisy_symbols)
        return demodulated_indices.to(current_device)  # Ensure output is on consistent device

    def sample(self, scores: torch.Tensor, codebook_mask=None, mod=None):
        # scores shape: (B, num_embeddings_K)
        # mod is expected to be a QAM instance or None

        dist_probs = F.softmax(scores, dim=-1)  # Soft probabilities

        if self.training:
            # Gumbel-Softmax for differentiable one-hot sampling
            samples_one_hot = F.gumbel_softmax(scores, tau=self.gumbel_tau, hard=True)  # (B, num_embeddings_K)

            if mod is not None:  # mod is the QAM instance
                # Construct noise for STE based on QAM channel effects
                noise_difference_ste = self.construct_noise(mod, codebook_mask, samples_one_hot)
                samples_one_hot = samples_one_hot + noise_difference_ste  # Apply STE-like noise
        else:  # Evaluation mode
            original_indices = torch.argmax(scores, dim=-1)  # (B,)

            if mod is not None:  # mod is the QAM instance
                # Pass indices through QAM channel simulation
                effective_indices = self._mod_channel_demod(mod, original_indices)
            else:
                effective_indices = original_indices

            # Determine num_classes for one_hot based on whether mask is active
            num_classes_for_one_hot = self.num_embeddings
            if codebook_mask is not None:
                # This part needs careful handling if mask changes the effective codebook size.
                print("Warning: GumbelSoftmaxQuantizer.sample (eval) codebook_mask logic is simplified.")
                pass
            samples_one_hot = F.one_hot(effective_indices, num_classes=num_classes_for_one_hot).float()

        return samples_one_hot, dist_probs

    def construct_noise(self, qam_modem_instance, codebook_mask, samples_one_hot: torch.Tensor) -> torch.Tensor:
        # samples_one_hot shape: (B, num_embeddings_K)
        # qam_modem_instance is the QAM class instance

        original_indices = torch.argmax(samples_one_hot, dim=-1)  # (B,)

        # Simulate passing original_indices through QAM modulation, channel, and demodulation
        noisy_indices = self._mod_channel_demod(qam_modem_instance, original_indices)  # (B,)

        # Determine the number of classes for one-hot encoding
        # This should be consistent with how `sample` and `recover` handle masks.
        current_num_classes = self.num_embeddings
        if codebook_mask is not None:
            # Example: if codebook_mask is a boolean tensor
            # current_num_classes = codebook_mask.sum().item()
            # And indices would need to be mapped to the masked space. This is complex.
            print("Warning: GumbelSoftmaxQuantizer.construct_noise codebook_mask logic is simplified.")
            pass

        one_hot_original = F.one_hot(original_indices, num_classes=current_num_classes).float()
        one_hot_noisy = F.one_hot(noisy_indices, num_classes=current_num_classes).float()

        # The noise is the difference, detached to act as a constant for STE
        noise_difference = (one_hot_noisy - one_hot_original).detach()
        return noise_difference

    def recover(self, samples_one_hot: torch.Tensor, codebook_mask=None) -> torch.Tensor:
        # samples_one_hot shape: (B, num_embeddings_K) or (B, num_masked_embeddings)
        # codebook shape: (num_embeddings_K, embedding_dim_D)

        current_codebook = self.codebook
        if codebook_mask is not None:
            # If mask is active, select relevant parts of the codebook.
            # current_codebook = self.codebook[codebook_mask] # If mask is boolean
            # samples_one_hot should also correspond to this masked codebook.
            print("Warning: GumbelSoftmaxQuantizer.recover codebook_mask logic is a placeholder.")
            pass

        quantized_output = torch.matmul(samples_one_hot, current_codebook)  # (B, embedding_dim_D)
        return quantized_output

    def forward(self, X_feat: torch.Tensor, codebook_mask=None, mod=None):
        # X_feat shape: (B, input_feature_dim)
        # mod is expected to be a QAM instance or None

        scores = self.compute_score(X_feat, codebook_mask)  # (B, num_embeddings_K)
        samples_one_hot, dist_probs = self.sample(
            scores, codebook_mask, mod=mod
        )  # samples_one_hot: (B, num_embeddings_K)
        quantized_output = self.recover(samples_one_hot, codebook_mask)  # (B, embedding_dim_D)

        return quantized_output, dist_probs
