import cv2  # OpenCV for image resizing
import gym
import numpy as np
from gym import spaces


class CartPoleImageWrapper(gym.Wrapper):
    def __init__(self, env, height=64, width=64, grayscale=True):
        super().__init__(env)
        self.height = height
        self.width = width
        self.grayscale = grayscale

        # Determine the number of channels for the observation space
        num_channels = 1 if grayscale else 3

        # Define the observation space: (Channels, Height, Width)
        # Normalized pixel values between 0.0 and 1.0
        self.observation_space = spaces.Box(low=0.0, high=1.0, shape=(num_channels, height, width), dtype=np.float32)

        # Check if the wrapped environment is set to render RGB arrays
        if self.env.render_mode != "rgb_array":
            # This is a critical requirement.
            # Ideally, this check is done when gym.make is called, but good to verify.
            raise ValueError(
                "CartPoleImageWrapper requires the base environment to be initialized with render_mode='rgb_array'."
            )

    def _preprocess_obs(self, obs_rgb_array: np.ndarray) -> np.ndarray:
        # obs_rgb_array is expected to be (H_orig, W_orig, 3)

        # 1. Resize the image
        img = cv2.resize(obs_rgb_array, (self.width, self.height), interpolation=cv2.INTER_AREA)
        # img shape is now (self.height, self.width, 3)

        # 2. Convert to grayscale if needed
        if self.grayscale:
            img = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
            # img shape is now (self.height, self.width)
            img = np.expand_dims(img, axis=-1)  # Add channel dim: (self.height, self.width, 1)

        # 3. Transpose from (H, W, C) to (C, H, W) for PyTorch CNNs
        img = np.transpose(img, (2, 0, 1))
        # img shape is now (num_channels, self.height, self.width)

        # 4. Normalize pixel values to [0.0, 1.0] and convert to float32
        img = img.astype(np.float32) / 255.0
        return img

    def reset(self, **kwargs):
        # The base environment's reset() returns (obs_vector, info) for CartPole
        _obs_vector, info = self.env.reset(**kwargs)

        # Get the RGB image observation
        rendered_obs_rgb = self.env.render()
        if rendered_obs_rgb is None:
            raise ValueError(
                "Environment's render() method returned None during reset. Ensure render_mode='rgb_array' is effective."
            )

        # Preprocess the image
        processed_image_obs = self._preprocess_obs(rendered_obs_rgb)
        return processed_image_obs

    def step(self, action):
        # The base environment's step() returns (obs_vector, reward, terminated, truncated, info)
        _obs_vector, reward, terminated, truncated, info = self.env.step(action)

        # Get the RGB image observation for the new state
        rendered_obs_rgb = self.env.render()
        if rendered_obs_rgb is None:
            raise ValueError(
                "Environment's render() method returned None during step. Ensure render_mode='rgb_array' is effective."
            )

        # Preprocess the image
        processed_image_obs = self._preprocess_obs(rendered_obs_rgb)
        return processed_image_obs, reward, terminated, info
