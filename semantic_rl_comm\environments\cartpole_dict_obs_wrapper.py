"""
提供Cartpole环境的step和reset方法的封装
包含了一个信念估计器
"""

import gym
import numpy as np
import torch


class CartpoleDictObsWrapper(gym.Wrapper):
    def __init__(self, env, config):
        super().__init__(env)
        self.config = config
        self.observation_space = gym.spaces.Dict(
            {
                "y": gym.spaces.Box(low=-np.inf, high=np.inf, shape=(config["embedding_dim_D"],), dtype=np.float32),
                "h": gym.spaces.Box(
                    low=-np.inf,
                    high=np.inf,
                    shape=(config["num_rnn_layers"], config["rnn_hidden_dim"]),
                    dtype=np.float32,
                ),
            }
        )
        if config["rnn_type"] == "lstm":
            self.observation_space.spaces["c"] = gym.spaces.Box(
                low=-np.inf, high=np.inf, shape=(config["num_rnn_layers"], config["rnn_hidden_dim"]), dtype=np.float32
            )
        self.encoder_agent = None
        self.device = config["device"]
        # 显式传递 action_space，确保类型不变
        self.action_space = env.action_space
        print(
            "[DEBUG] CartpoleDictObsWrapper action_space:",
            self.action_space,
            type(self.action_space),
            isinstance(self.action_space, gym.spaces.Discrete),
        )

    def reset(self, **kwargs):
        obs, info = self.env.reset(**kwargs)
        obs_tensor = torch.tensor(obs, dtype=torch.float32, device=self.device).unsqueeze(0)
        with torch.no_grad():
            Y_t, _ = self.encoder_agent.encode_and_channel(obs_tensor)
        h = torch.zeros(self.config["num_rnn_layers"], self.config["rnn_hidden_dim"], device=self.device)
        c = (
            torch.zeros(self.config["num_rnn_layers"], self.config["rnn_hidden_dim"], device=self.device)
            if self.config["rnn_type"] == "lstm"
            else None
        )
        dict_obs = {"y": Y_t.squeeze(0).cpu().numpy(), "h": h.cpu().numpy()}
        if c is not None:
            dict_obs["c"] = c.cpu().numpy()
        return dict_obs  # 只返回 obs，不返回 info

    def step(self, action):
        obs, reward, terminated, truncated = self.env.step(action)
        obs_tensor = torch.tensor(obs, dtype=torch.float32, device=self.device).unsqueeze(0)
        with torch.no_grad():
            Y_t, dist = self.encoder_agent.encode_and_channel(obs_tensor)
        # 这里h/c应由外部管理和传递，简化处理
        h = torch.zeros(self.config["num_rnn_layers"], self.config["rnn_hidden_dim"], device=self.device)
        c = (
            torch.zeros(self.config["num_rnn_layers"], self.config["rnn_hidden_dim"], device=self.device)
            if self.config["rnn_type"] == "lstm"
            else None
        )
        dict_obs = {"y": Y_t.squeeze(0).cpu().numpy(), "h": h.cpu().numpy()}
        if c is not None:
            dict_obs["c"] = c.cpu().numpy()
        return dict_obs, reward, terminated, truncated
