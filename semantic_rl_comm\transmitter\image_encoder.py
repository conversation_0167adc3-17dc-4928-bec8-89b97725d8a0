import torch
import torch.nn as nn
import torch.nn.functional as F # Potentially useful for ReLUs etc.

class ImageCNNEncoder(nn.Module):
    def __init__(self, input_channels, feature_dim, cnn_config=None):
        """
        Args:
            input_channels (int): Number of input channels in the image (e.g., 2 for 2 stacked grayscale frames).
            feature_dim (int): The dimension of the output feature vector z_raw(t).
            cnn_config (dict, optional): Configuration for CNN layers. 
                                         Example: {'layers': [
                                             {'type': 'conv', 'out_channels': 32, 'kernel': 8, 'stride': 4},
                                             {'type': 'relu'},
                                             {'type': 'conv', 'out_channels': 64, 'kernel': 4, 'stride': 2},
                                             {'type': 'relu'},
                                             {'type': 'conv', 'out_channels': 64, 'kernel': 3, 'stride': 1},
                                             {'type': 'relu'},
                                             {'type': 'flatten'},
                                             # FC layer to map to feature_dim will be added after flatten
                                         ]}
                                         If None, a default simple architecture will be used.
        """
        super().__init__()
        self.input_channels = input_channels
        self.feature_dim = feature_dim

        layers = []
        layers.append(nn.Conv2d(input_channels, 32, kernel_size=8, stride=4)) # Output: (N, 32, 20, 20) for 84x84
        layers.append(nn.ReLU())
        layers.append(nn.Conv2d(32, 64, kernel_size=4, stride=2)) # Output: (N, 64, 9, 9)
        layers.append(nn.ReLU())
        layers.append(nn.Conv2d(64, 64, kernel_size=3, stride=1)) # Output: (N, 64, 7, 7)
        layers.append(nn.ReLU())
        layers.append(nn.Flatten())

        # 计算flattened size
        dummy_input_H, dummy_input_W = 84, 84 
        flattened_size = self._get_conv_output_size(dummy_input_H, dummy_input_W, layers)
        layers.append(nn.Linear(flattened_size, feature_dim))
        layers.append(nn.ReLU()) # Often a ReLU after the FC layer too

        self.cnn_layers = nn.Sequential(*layers)

    def _get_conv_output_size(self, H_in, W_in, conv_layers_list):
        temp_model_layers = []
        for layer in conv_layers_list:
            if isinstance(layer, (nn.Conv2d, nn.ReLU, nn.MaxPool2d, nn.AvgPool2d, nn.Flatten)):
                 temp_model_layers.append(layer)
            if isinstance(layer, nn.Linear): # Stop if we hit a linear layer
                break
        
        temp_model = nn.Sequential(*temp_model_layers)
        
        with torch.no_grad():
            dummy_input = torch.zeros(1, self.input_channels, H_in, W_in)
            output_tensor = temp_model(dummy_input)
        return output_tensor.shape[1] # Shape after flatten is (batch_size, flattened_features)

    def forward(self, obs_images):
        """
        Args:
            obs_images (torch.Tensor): Batch of observation images.
                                       Shape: [batch_size, input_channels, H, W].
        Returns:
            torch.Tensor: Batch of feature vectors z_raw.
                          Shape: [batch_size, feature_dim].
        """
        # 直接处理，在外部进行图像预处理
        z_raw = self.cnn_layers(obs_images)
        return z_raw

if __name__ == '__main__':
    # Example Usage:
    # Assuming input images are 2 stacked 84x84 grayscale frames
    input_channels_example = 2
    feature_dim_example = 256 # Example output feature dimension
    img_h, img_w = 84, 84

    # Test with default config
    encoder_default = ImageCNNEncoder(input_channels=input_channels_example, feature_dim=feature_dim_example)
    print("Default Encoder:")
    print(encoder_default)
    dummy_obs_default = torch.randn(4, input_channels_example, img_h, img_w) # Batch of 4
    features_default = encoder_default(dummy_obs_default)
    print("Output features shape (default):", features_default.shape) # Expected: [4, feature_dim_example]
    assert features_default.shape == (4, feature_dim_example)

    # Test with a custom config (example)
    custom_cnn_config = {
        'layers': [
            {'type': 'conv', 'out_channels': 16, 'kernel': 5, 'stride': 2}, # 16x40x40
            {'type': 'relu'},
            {'type': 'conv', 'out_channels': 32, 'kernel': 3, 'stride': 2}, # 32x19x19
            {'type': 'relu'},
            {'type': 'flatten'},
            # The _get_conv_output_size and __init__ would need to be more robust
            # to automatically add the final Linear layer based on this config.
            # For this example, let's assume the ImageCNNEncoder's default logic
            # for adding the final Linear layer is used, or the config itself
            # would specify a Linear layer to feature_dim_example.
            # The provided skeleton's default config path is more complete for now.
        ]
    }
    # The current skeleton's custom config path is not fully implemented to auto-add the final Linear layer.
    # So, this custom_config test might not work perfectly without adjusting the __init__ logic for config parsing.
    # For now, the default path is more robustly defined in the skeleton.
    # encoder_custom = ImageCNNEncoder(input_channels=input_channels_example, 
    #                                 feature_dim=feature_dim_example, 
    #                                 cnn_config=custom_cnn_config)
    # print("\nCustom Encoder (Illustrative - may need __init__ refinement for config parsing):")
    # print(encoder_custom)
    # dummy_obs_custom = torch.randn(4, input_channels_example, img_h, img_w)
    # features_custom = encoder_custom(dummy_obs_custom)
    # print("Output features shape (custom):", features_custom.shape)