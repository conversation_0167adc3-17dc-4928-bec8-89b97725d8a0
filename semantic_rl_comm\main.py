import gym  # 强制使用 gym 替换 gymnasium
import numpy as np
import torch
import torch.optim as optim
from channel.channel_models import Channel

# Import project modules (assuming they are in the semantic_rl_comm package)
from config import get_config  # Assuming you'll have a function in config.py to load params
from environments.cartpole_dict_obs_wrapper import CartpoleDictObsWrapper
from environments.custom_cartpole_img import CustomCartpoleImgEnv
from receiver.belief_estimator import BeliefEstimatorRNN  # Not directly used if BeliefRnnFeaturesExtractor handles it
from receiver.sac_agent import BeliefRnnFeaturesExtractor  # Key for SAC
from replay_buffer import CustomReplayBuffer
from stable_baselines3 import PPO  # 改为PPO
from stable_baselines3.common.callbacks import BaseCallback  # For custom logging or actions
from transmitter.encoder_agent import EncoderAgent

# from logger import Logger # Assuming a Logger class
from utils import set_seed

# from your_channel_module import RayleighChannel  # TODO: [实现 RayleighChannel 或替换为其他信道模型] 请确保你有 RayleighChannel 的实现或替换为其他信道模型


# --- Main Training Function ---
def train():
    # 1. Load Configuration
    config = get_config()
    print(f"Using device: {config['device']}")

    # 2. Set Random Seed
    set_seed(config["seed"])

    # 3. Initialize Environment
    # 环境需要输出与BeliefRnnFeaturesExtractor兼容的Dict观测（'y', 'h', 'c'）

    # 创建基础环境
    base_env = CustomCartpoleImgEnv(
        frame_stack=config["frame_stack"], screen_width=config["img_width"], screen_height=config["img_height"]
    )
    # 包装成Dict观测环境
    env = CartpoleDictObsWrapper(base_env, config)
    # 检查 action_space 类型
    print(
        "[DEBUG] env.action_space:",
        env.action_space,
        type(env.action_space),
        isinstance(env.action_space, gym.spaces.Discrete),
    )
    # 后续在初始化encoder_agent和channel_model后，需调用env.set_encoder_and_channel(encoder_agent, channel_model)

    # 4.初始化编码器和
    encoder_agent = EncoderAgent(
        input_channels=config["frame_stack"] * 1,  # Assuming 1 channel per frame (e.g. grayscale)
        cnn_feature_dim=config["cnn_feature_dim"],
        num_embeddings_K=config["num_embeddings_K"],
        embedding_dim_D=config["embedding_dim_D"],
        snr_db_train=config["snr_db_train"],
        gumbel_tau=config["gumbel_tau"],
        lambda_mi=config["lambda_mi"],
        gamma_enc=config["gamma_enc"],
        device=config["device"],
    ).to(config["device"])

    # Set encoder and channel for the environment wrapper
    env.encoder_agent = encoder_agent

    # 6. Initialize Receiver (PPO Agent with BeliefRnnFeaturesExtractor)
    policy_kwargs = dict(
        features_extractor_class=BeliefRnnFeaturesExtractor,
        features_extractor_kwargs=dict(
            features_dim=config["sac_features_dim"],  # This is belief_dim
            rnn_input_dim_Y=config["embedding_dim_D"],  # Y_t's dim
            rnn_hidden_dim=config["rnn_hidden_dim"],
            rnn_type=config["rnn_type"],
            num_rnn_layers=config["num_rnn_layers"],
            device=config["device"],
        ),
        net_arch=[256, 256],  # Example MLP architecture for actor/critic after feature extraction
    )

    # PPO 支持离散动作空间
    ppo_model = PPO(
        "MultiInputPolicy",  # ← 使用 MultiInputPolicy 以支持 Dict 观测空间
        env,
        policy_kwargs=policy_kwargs,
        verbose=1,
        batch_size=config["batch_size_sac"],
        gamma=config["gamma_sac"],
        n_steps=2048,  # PPO特有参数，可根据需要调整
        learning_rate=config["lr_sac_actor"],
        seed=config["seed"],
    )

    # 7. Initialize Optimizers
    optimizer_encoder = optim.Adam(encoder_agent.parameters(), lr=config["lr_encoder"])
    # PPO model has its own optimizers for actor and critic.

    # 8. Initialize Custom Replay Buffer (if used for encoder or shared)
    # This buffer would store O_t, X_t, Y_t, belief_t, h_t, c_t, etc.
    custom_replay_buffer = CustomReplayBuffer(
        buffer_size=config["buffer_size"],
        observation_space_env=base_env.observation_space,  # For O_t
        action_space_env=base_env.action_space,
        X_dim=config["embedding_dim_D"],
        Y_dim=config["embedding_dim_D"],
        belief_dim=config["sac_features_dim"],
        rnn_hidden_dim=config["rnn_hidden_dim"],
        num_rnn_layers=config["num_rnn_layers"],
        rnn_type=config["rnn_type"],
        dist_mi_dim=config["num_embeddings_K"],  # For logits from quantizer
        device=config["device"],
    )

    # 9. Initialize Logger
    # logger = Logger(log_dir=f"logs/run_{config['seed']}_{datetime.now().strftime('%Y%m%d_%H%M%S']}")
    # For now, simple print
    # TODO: [实现完整的日志记录器初始化和使用，替换当前的占位符打印]
    print("Logger initialized (placeholder).")

    # 10. Training Loop
    print("Starting training loop...")
    obs_O_t, _ = base_env.reset(seed=config["seed"])  # Original image observation

    # Initialize RNN hidden states for the very first step
    # These need to be part of the observation for the PPO agent / BeliefRnnFeaturesExtractor
    current_h, current_c = None, None  # Will be initialized by BeliefEstimatorRNN on first pass if None
    if config["rnn_type"] == "lstm":
        # Dummy initial hidden states for the PPO observation dict
        # These should ideally be managed per-episode.
        # The BeliefRnnFeaturesExtractor will get these via the observation dict.
        # For the first step, they can be zeros.
        initial_h = torch.zeros(config["num_rnn_layers"], config["rnn_hidden_dim"], device=config["device"])
        initial_c = torch.zeros(config["num_rnn_layers"], config["rnn_hidden_dim"], device=config["device"])
    else:  # GRU
        initial_h = torch.zeros(config["num_rnn_layers"], config["rnn_hidden_dim"], device=config["device"])
        initial_c = None  # Not used for GRU

    # Conceptual:
    ppo_obs = {
        "y": torch.zeros(config["embedding_dim_D"], device=config["device"]),  # Placeholder Y_t for first step
        "h": initial_h.unsqueeze(0) if initial_h is not None else None,  # Add batch dim for SB3
        "c": initial_c.unsqueeze(0) if initial_c is not None else None,  # Add batch dim for SB3
    }
    num_episodes = 0
    timesteps_since_last_log = 0

    # Main loop (simplified, SB3's model.learn() handles its internal loop)
    # This loop is more for illustrating data collection for the encoder and custom buffer.
    # If PPO is trained independently, its model.learn() would be called.
    # If trained jointly in a more custom loop:

    for timestep in range(1, config["total_timesteps"] + 1):
        # --- Interaction Phase ---
        # 1. Encode O_t -> X_t using EncoderAgent
        # obs_O_t_tensor = torch.as_tensor(obs_O_t, device=config["device"]).float().unsqueeze(0) # Add batch dim
        # if obs_O_t_tensor.ndim == 3: obs_O_t_tensor = obs_O_t_tensor.unsqueeze(0) # B,C,H,W
        # Ensure correct shape for encoder_agent [B, C, H, W]
        # Assuming obs_O_t is already [C,H,W] from env.reset/step
        obs_O_t_tensor = torch.tensor(obs_O_t, dtype=torch.float32, device=config["device"]).unsqueeze(0)

        Y_t, logits_for_mi = encoder_agent.encode_and_channel(obs_O_t_tensor)  # X_t: [1, D_codebook], logits: [1, K]

        # 3. Construct observation for PPO agent (using Y_t and current RNN states)
        # This is where the Gym Wrapper would be essential.
        # The wrapper's step/reset would return this dict.
        # For this loop, we manage h,c explicitly.

        # BeliefEstimatorRNN (inside features extractor) will take Y_t and (current_h, current_c)
        # and output belief_state (b_t) and next_h, next_c.
        # The PPO agent's observation needs to be a dictionary.

        # Prepare observation for PPO model.predict()
        # This requires careful handling of RNN states if not using SB3's RecurrentActorCriticPolicy
        # With a custom features extractor, SB3 expects the full observation dict at each step.
        # The RNN states (h,c) must be part of this dict.

        # For the first step of an episode, current_h, current_c might be None or zeros.
        # BeliefEstimatorRNN's forward method handles initialization if h_prev, c_prev are None.

        # Create the observation dictionary for PPO
        # The shapes for h and c need to be [num_layers, batch_size=1, hidden_dim] for BeliefEstimatorRNN
        # but SB3's Dict observation space might expect [batch_size=1, num_layers, hidden_dim] or flattened.
        # Our BeliefRnnFeaturesExtractor handles the permute if it receives [B,L,H].

        # Let's assume current_h, current_c are maintained with shape [L, B, H]
        # For the first step, they are initialized.
        if timestep == 1 or done:  # Start of episode or after a done
            if config["rnn_type"] == "lstm":
                current_h, current_c = (
                    encoder_agent.quantizer.codebook.weight.new_zeros(
                        config["num_rnn_layers"], 1, config["rnn_hidden_dim"]
                    ),
                    encoder_agent.quantizer.codebook.weight.new_zeros(
                        config["num_rnn_layers"], 1, config["rnn_hidden_dim"]
                    ),
                )
            else:  # GRU
                current_h = encoder_agent.quantizer.codebook.weight.new_zeros(
                    config["num_rnn_layers"], 1, config["rnn_hidden_dim"]
                )
                current_c = None

        # The observation for PPO's features_extractor
        ppo_obs_dict_for_extractor = {
            "y": Y_t.squeeze(0).cpu().detach().numpy(),
            "h": current_h.permute(1, 0, 2).cpu().detach().numpy() if current_h is not None else None,
        }
        if config["rnn_type"] == "lstm":
            ppo_obs_dict_for_extractor["c"] = (
                current_c.permute(1, 0, 2).cpu().detach().numpy() if current_c is not None else None
            )

        # Remove 'c' if GRU and it's None (though extractor handles None)
        if config["rnn_type"] == "gru" and "c" in ppo_obs_dict_for_extractor:
            if ppo_obs_dict_for_extractor["c"] is None:
                del ppo_obs_dict_for_extractor["c"]

        # The features_extractor will internally call belief_estimator
        # belief_state_t, next_h_for_buffer, next_c_for_buffer = belief_estimator(Y_t, current_h, current_c)
        # This happens inside ppo_model.predict() or when features are extracted.
        # We need these states for the replay buffer.
        # This is tricky: SB3's predict doesn't easily give back intermediate states from extractor.
        # One way: The features_extractor itself could store the latest h_next, c_next.
        # Or, we run belief_estimator once here to get them for storage, then PPO runs it again. (Inefficient)

        # Let's assume for now that we can get belief_state_t, next_h, next_c
        # This part needs careful integration with how SB3 calls the extractor.
        # TODO: [实现从 SB3 特征提取器中正确获取或传递 next_h 和 next_c 的机制，避免当前模拟获取的方式]
        # For simplicity in this skeleton, let's simulate getting these:
        # This would ideally be a more integrated step.
        with torch.no_grad():
            # Simulate what the feature extractor does to get belief and next RNN states
            # This is for storage. PPO will do its own pass.
            temp_h_for_belief = current_h.detach().clone() if current_h is not None else None
            temp_c_for_belief = current_c.detach().clone() if current_c is not None else None

            belief_state_t, h_for_next_step, c_for_next_step = ppo_model.policy.features_extractor.belief_estimator(
                Y_t, temp_h_for_belief, temp_c_for_belief
            )
            # belief_state_t is [1, belief_dim]
            # h_for_next_step, c_for_next_step are [L,1,H]

        # 4. PPO Agent selects action a_t based on its observation (which uses belief_state_t internally)
        # SB3's predict needs the observation in the format defined by ppo_obs_space (the Dict)
        # It will then call the features_extractor.
        action, _ppo_hidden_states = ppo_model.predict(ppo_obs_dict_for_extractor, deterministic=False)
        # action is numpy here
        if isinstance(action, np.ndarray):
            action = action.item()

        # 5. Environment step
        next_obs_O_t, reward, done, info = base_env.step(action)
        # done 直接用于后续逻辑

        # Store next RNN states for the buffer (these are h_for_next_step, c_for_next_step)
        # These will become current_h, current_c for the *next* iteration's ppo_obs_dict.

        # --- Storage Phase (Custom Replay Buffer) ---
        # Convert tensors to numpy for storage, ensure correct shapes for single transition
        custom_replay_buffer.add(
            obs_O_t=obs_O_t,  # Original image
            X_t=X_t.squeeze(0).detach().cpu().numpy(),
            Y_t=Y_t.squeeze(0).detach().cpu().numpy(),
            belief_t=belief_state_t.squeeze(0).detach().cpu().numpy(),
            h_t=current_h.squeeze(1).detach().cpu().numpy()
            if current_h is not None
            else np.zeros((config["num_rnn_layers"], config["rnn_hidden_dim"])),  # Store as [L,H]
            c_t=current_c.squeeze(1).detach().cpu().numpy()
            if current_c is not None and config["rnn_type"] == "lstm"
            else None,  # Store as [L,H]
            action=action,  # Already numpy
            reward=float(reward),
            next_obs_O_t=next_obs_O_t,
            # For next_belief_t, next_h_t, next_c_t, we need to compute them based on next_obs_O_t
            # This is complex as it requires another pass through encoder, channel, belief_estimator
            # Or, store Y_{t+1} and compute b_{t+1} during sampling.
            # For now, let's store placeholder for next_belief_t and next_rnn_states.
            # This is a common challenge in such end-to-end systems.
            # A simpler approach for PPO might be to just use its internal buffer if it can handle Dict obs.
            # If CustomReplayBuffer is primarily for the encoder, it might not need full next_belief states.
            # Let's assume we calculate next_belief_state and next_rnn_hidden_state for the buffer:
            # This would involve:
            # next_obs_O_t_tensor = torch.tensor(next_obs_O_t, dtype=torch.float32, device=config["device"]).unsqueeze(0)
            # next_X_t, _ = encoder_agent.encode(next_obs_O_t_tensor)
            # next_Y_t = channel_model(next_X_t)
            # next_belief_state_val, next_h_for_buffer_val, next_c_for_buffer_val = \
            #     ppo_model.policy.features_extractor.belief_estimator(next_Y_t, h_for_next_step, c_for_next_step)
            # For simplicity in this skeleton, let's use placeholders or re-use current for 'next' versions
            # TODO: [正确实现 next_belief_t, next_h_t, next_c_t 的计算和存储，而非使用占位符] This part MUST be correctly implemented for proper training.
            next_belief_t=belief_state_t.squeeze(0).detach().cpu().numpy(),  # Placeholder!
            next_h_t=h_for_next_step.squeeze(1).detach().cpu().numpy()
            if h_for_next_step is not None
            else np.zeros((config["num_rnn_layers"], config["rnn_hidden_dim"])),  # Placeholder!
            next_c_t=c_for_next_step.squeeze(1).detach().cpu().numpy()
            if c_for_next_step is not None and config["rnn_type"] == "lstm"
            else None,  # Placeholder!
            done=done,
            dist_mi=logits_for_mi.squeeze(0).detach().cpu().numpy(),
        )

        # Update current observation and RNN states for next iteration
        obs_O_t = next_obs_O_t
        current_h = h_for_next_step.detach()  # Keep on device, [L,1,H]
        current_c = c_for_next_step.detach() if c_for_next_step is not None else None  # Keep on device, [L,1,H]

        # --- Learning Phase ---
        if timestep > config["learning_starts"]:
            # Sample from CustomReplayBuffer for Encoder training
            if len(custom_replay_buffer) > config["batch_size_sac"]:
                encoder_batch = custom_replay_buffer.sample(config["batch_size_sac"])
                O_enc = encoder_batch["obs_O"].to(config["device"])
                rewards_enc = encoder_batch["rewards"].to(config["device"])

                # 重新 forward 得到 X_t, logits, Y_t
                X_enc, logits_enc = encoder_agent.encode(O_enc)
                Y_enc = channel_model(X_enc)

                print("rewards_enc mean:", rewards_enc.mean().item(), "std:", rewards_enc.std().item())
                print("X_enc mean:", X_enc.mean().item(), "std:", X_enc.std().item())
                print("logits_enc mean:", logits_enc.mean().item(), "std:", logits_enc.std().item())
                print("Y_enc mean:", Y_enc.mean().item(), "std:", Y_enc.std().item())

                encoder_loss = encoder_agent.compute_loss(
                    rewards_batch=rewards_enc,
                    logits_batch=logits_enc,
                    X_batch=X_enc,
                    Y_batch=Y_enc,
                    O_batch_or_Ze_batch=O_enc,
                )
                optimizer_encoder.zero_grad()
                encoder_loss.backward()
                optimizer_encoder.step()

                if timestep % 100 == 0:
                    print(f"Timestep: {timestep}, Encoder Loss: {encoder_loss.item():.4f}")

            # PPO Training (using its own replay buffer and learning schedule)
            # SB3's model.train() is usually called internally by model.learn。
            # 这里直接调用learn，每次训练一个step，reset_num_timesteps=False可累计训练步数
            if timestep % 1 == 0:
                ppo_model.learn(total_timesteps=1, reset_num_timesteps=False)

        if done:
            num_episodes += 1
            print(
                f"Episode {num_episodes} finished after {timestep % config['log_interval'] if config['log_interval'] > 0 else timestep} timesteps."
            )
            obs_O_t, _ = base_env.reset()
            # RNN states (current_h, current_c) will be reset at the start of the next loop iteration if done.
            timesteps_since_last_log = 0

        if timestep % config["log_interval"] == 0:
            print(f"Timestep: {timestep}/{config['total_timesteps']}")
            # TODO: [添加更详细和规范的训练过程日志记录，例如平均回报、损失函数值等] Add proper logging (e.g., episode reward, mean reward)
            # logger.log_scalar("rollout/ep_reward_mean", ..., timestep)
            timesteps_since_last_log = 0

        if timestep % config["model_save_freq"] == 0:
            print(f"Saving model at timestep {timestep}")
            # TODO: [实现模型（PPO agent 和 Encoder agent）的保存逻辑]
            # ppo_model.save(f"models/ppo_model_ts{timestep}")
            # torch.save(encoder_agent.state_dict(), f"models/encoder_agent_ts{timestep}.pth")
            pass  # Placeholder for saving

    print("Training finished.")
    # TODO: [在训练结束后正确关闭环境和日志记录器]
    # base_env.close()
    # logger.close()


if __name__ == "__main__":
    try:
        train()
    except Exception as e:
        print(f"An error occurred during the conceptual training run: {e}")
        import traceback

        traceback.print_exc()
        print("\nThis main.py is a skeleton and requires further implementation,")
        print("especially around environment wrapping and replay buffer management for SB3.")
