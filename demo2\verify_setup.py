"""
验证项目设置是否正确的脚本。
"""

import torch
import gym
import sys
import os

def verify_imports():
    """验证所有必要的模块都能正确导入。"""
    print("验证模块导入...")
    
    try:
        from env_wrapper import CartPoleImageWrapper
        print("✅ env_wrapper 导入成功")
        
        from networks.actor_critic import ActorCriticNetwork
        print("✅ networks.actor_critic 导入成功")
        
        from ppo_trainer import PPOTrainer
        print("✅ ppo_trainer 导入成功")
        
        from rollout_buffer import RolloutBuffer
        print("✅ rollout_buffer 导入成功")
        
        from channel import PSK
        print("✅ channel 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def verify_basic_functionality():
    """验证基本功能是否正常。"""
    print("\n验证基本功能...")
    
    try:
        # 导入模块
        from env_wrapper import CartPoleImageWrapper
        from networks.actor_critic import ActorCriticNetwork
        
        # 创建环境
        env = gym.make('CartPole-v1', render_mode='rgb_array')
        env = CartPoleImageWrapper(env, height=64, width=64, grayscale=True)
        
        obs_shape = env.observation_space.shape
        action_dim = env.action_space.n
        
        print(f"✅ 环境创建成功: 观测形状 {obs_shape}, 动作维度 {action_dim}")
        
        # 创建模型
        model = ActorCriticNetwork(
            obs_shape=obs_shape,
            action_dim=action_dim,
            latent_dim=64,
            num_embeddings=16
        )
        
        print(f"✅ 模型创建成功: {sum(p.numel() for p in model.parameters()):,} 参数")
        
        # 测试前向传播
        obs = env.reset()
        obs_tensor = torch.tensor(obs, dtype=torch.float32).unsqueeze(0)
        
        with torch.no_grad():
            action, log_prob, value = model.act(obs_tensor)
            
        print(f"✅ 前向传播成功: 动作={action}, 价值={value:.3f}")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"❌ 功能验证失败: {e}")
        return False

def verify_training_setup():
    """验证训练设置是否正确。"""
    print("\n验证训练设置...")
    
    try:
        from env_wrapper import CartPoleImageWrapper
        from networks.actor_critic import ActorCriticNetwork
        from ppo_trainer import PPOTrainer
        from rollout_buffer import RolloutBuffer
        
        # 创建所有组件
        env = gym.make('CartPole-v1', render_mode='rgb_array')
        env = CartPoleImageWrapper(env, height=64, width=64, grayscale=True)
        
        model = ActorCriticNetwork(
            obs_shape=env.observation_space.shape,
            action_dim=env.action_space.n,
            latent_dim=64,
            num_embeddings=16
        )
        
        trainer = PPOTrainer(model=model, device=torch.device('cpu'))
        
        buffer = RolloutBuffer(
            buffer_size=32,
            obs_shape=env.observation_space.shape,
            device=torch.device('cpu')
        )
        
        print("✅ 所有训练组件创建成功")
        
        # 测试一个简单的数据收集
        obs = env.reset()
        obs_tensor = torch.tensor(obs, dtype=torch.float32).unsqueeze(0)
        action, log_prob, value = model.act(obs_tensor)
        
        buffer.add(
            obs=obs,
            action=action,
            log_prob=log_prob.item(),
            reward=1.0,
            done=False,
            value=value.item()
        )
        
        print("✅ 数据收集测试成功")
        
        env.close()
        return True
        
    except Exception as e:
        print(f"❌ 训练设置验证失败: {e}")
        return False

def main():
    """运行所有验证。"""
    print("=" * 50)
    print("项目设置验证")
    print("=" * 50)
    
    print(f"Python 版本: {sys.version}")
    print(f"PyTorch 版本: {torch.__version__}")
    print(f"工作目录: {os.getcwd()}")
    print()
    
    # 运行所有验证
    all_passed = True
    
    if not verify_imports():
        all_passed = False
    
    if not verify_basic_functionality():
        all_passed = False
        
    if not verify_training_setup():
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有验证通过！项目设置正确。")
        print("可以运行以下命令:")
        print("  python tests/test_implementation.py  # 完整测试")
        print("  python tests/quick_train_test.py     # 快速训练测试")
        print("  python train.py                      # 完整训练")
    else:
        print("❌ 验证失败！请检查项目设置。")
    print("=" * 50)

if __name__ == "__main__":
    main()
