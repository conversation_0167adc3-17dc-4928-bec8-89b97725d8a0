"""
带共享编码器的 Actor-Critic 网络，用于 PPO 训练。
集成了图像编码、量化和策略/价值输出头。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional
from .encoder_decoder import ImageEncoder, ImageDecoder, VectorQuantizer


class ActorCriticNetwork(nn.Module):
    """
    统一的 Actor-Critic 网络，带有共享图像编码器。
    
    架构:
    输入图像 -> 编码器 -> 量化器 -> [策略头, 价值头]
                                -> 解码器 (用于重构损失)
    """
    
    def __init__(
        self,
        obs_shape: Tuple[int, int, int],  # (C, H, W)
        action_dim: int,
        latent_dim: int = 64,
        num_embeddings: int = 16,
        gumbel_tau: float = 1.0,
        qam_modem = None
    ):
        super().__init__()
        
        self.obs_shape = obs_shape
        self.action_dim = action_dim
        self.latent_dim = latent_dim
        self.num_embeddings = num_embeddings
        self.qam_modem = qam_modem
        
        C, H, W = obs_shape
        
        # 核心组件
        self.encoder = ImageEncoder(input_channels=C, feature_dim=latent_dim)
        self.quantizer = VectorQuantizer(
            num_embeddings=num_embeddings,
            embedding_dim=latent_dim,
            gumbel_tau=gumbel_tau
        )
        self.decoder = ImageDecoder(feature_dim=latent_dim, output_channels=C)
        
        # 策略和价值输出头
        self.policy_head = nn.Linear(latent_dim, action_dim)
        self.value_head = nn.Linear(latent_dim, 1)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化策略和价值头权重"""
        # 策略头 - 较小的初始化以获得稳定的梯度
        nn.init.orthogonal_(self.policy_head.weight, gain=0.01)
        nn.init.constant_(self.policy_head.bias, 0)
        
        # 价值头
        nn.init.orthogonal_(self.value_head.weight, gain=1.0)
        nn.init.constant_(self.value_head.bias, 0)
    
    def encode_and_quantize(self, obs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        编码观测并量化特征。
        
        Args:
            obs: 形状为 (B, C, H, W) 的观测
        
        Returns:
            (量化特征, 分布概率) 的元组
        """
        # 将图像编码为潜在特征
        latent = self.encoder(obs)  # (B, latent_dim)
        
        # 量化特征
        z_q, dist_probs = self.quantizer(latent, mod=self.qam_modem)  # (B, latent_dim), (B, num_embeddings)
        
        return z_q, dist_probs
    
    def forward(self, obs: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        训练时的完整前向传播。
        
        Args:
            obs: 形状为 (B, C, H, W) 的观测
        
        Returns:
            (logits, values, z_q, dist_probs) 的元组
            - logits: 形状为 (B, action_dim) 的动作 logits
            - values: 形状为 (B, 1) 的状态价值
            - z_q: 形状为 (B, latent_dim) 的量化特征
            - dist_probs: 形状为 (B, num_embeddings) 的量化器分布
        """
        # 编码和量化
        z_q, dist_probs = self.encode_and_quantize(obs)
        
        # 策略和价值输出
        logits = self.policy_head(z_q)  # (B, action_dim)
        values = self.value_head(z_q)   # (B, 1)
        
        return logits, values, z_q, dist_probs
    
    def act(self, obs: torch.Tensor) -> Tuple[int, torch.Tensor, torch.Tensor]:
        """
        为环境交互采样动作。
        
        Args:
            obs: 形状为 (1, C, H, W) 或 (C, H, W) 的单个观测
        
        Returns:
            (action, log_prob, value) 的元组
            - action: 采样的动作 (int)
            - log_prob: 动作的对数概率
            - value: 状态价值估计
        """
        if obs.dim() == 3:  # 如果需要，添加批次维度
            obs = obs.unsqueeze(0)
        
        with torch.no_grad():
            logits, values, _, _ = self.forward(obs)
            
            # 从策略中采样动作
            action_probs = F.softmax(logits, dim=-1)
            dist = torch.distributions.Categorical(action_probs)
            action = dist.sample()
            log_prob = dist.log_prob(action)
            
            return action.item(), log_prob.squeeze(), values.squeeze()
    
    def evaluate_actions(self, obs: torch.Tensor, actions: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        为 PPO 损失计算评估动作。
        
        Args:
            obs: 形状为 (B, C, H, W) 的观测
            actions: 形状为 (B,) 的动作
        
        Returns:
            (log_probs, values, entropy) 的元组
        """
        logits, values, _, _ = self.forward(obs)
        
        # 计算动作概率和分布
        action_probs = F.softmax(logits, dim=-1)
        dist = torch.distributions.Categorical(action_probs)
        
        # 评估动作
        log_probs = dist.log_prob(actions)
        entropy = dist.entropy()
        
        return log_probs, values.squeeze(), entropy
    
    def reconstruct(self, obs: torch.Tensor) -> torch.Tensor:
        """
        重构图像用于重构损失。
        
        Args:
            obs: 形状为 (B, C, H, W) 的观测
        
        Returns:
            形状为 (B, C, H, W) 的重构图像
        """
        z_q, _ = self.encode_and_quantize(obs)
        return self.decoder(z_q)
