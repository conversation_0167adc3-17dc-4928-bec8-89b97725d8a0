import numpy as np
import torch
import torch.nn as nn


class Channel:
    def __init__(self, M, SNR):
        self.M = M
        self.max = int(np.sqrt(self.M))-1
        self.constellation, self.map = self.build()
        self.p = (M-1)/6
        self.delta = self.compute_noise(SNR)

    def compute_noise(self, SNR):
        delta_2 = self.p/torch.pow(torch.tensor(10), SNR/10).float()
        return torch.sqrt(delta_2/2)

    def build(self):
        l = []
        d = {}
        m = int(np.sqrt(self.M))
        for i in range(m):
            for j in range(m):
                l.append((i,j))

        for i in range(self.M):
            d[l[i]] = i
        return l, d

    def modulate(self, z:torch.Tensor):
        m = z.shape[0]
        # print(m)
        X = torch.ones(int(m), 2)
        for i in range(m):
            x, y = self.constellation[int(z[i])]
            X[i,0] = x
            X[i,1] = y
        return X

    def pass_channel(self, X):
        X += self.delta*torch.randn_like(X)
        return X

    def demodulate(self, X):
        m = X.shape[0]
        Z = torch.ones(m).long()
        for i in range(m):
            x = self.assign(X[i,0])
            y = self.assign(X[i,1])
            Z[i] = self.map[(x,y)]
        return Z

    def assign(self, ele):
        num = int(torch.round(ele))
        if num > self.max:
            num = self.max
        if num < 0:
            num = 0
        return num
