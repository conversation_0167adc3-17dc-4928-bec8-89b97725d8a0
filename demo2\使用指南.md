# 使用指南

## 快速开始

### 1. 验证环境设置
```bash
python verify_setup.py
```
这将验证所有依赖和模块是否正确安装和配置。

### 2. 运行测试
```bash
# 完整功能测试
python tests/test_implementation.py

# 快速训练测试 (推荐先运行)
python tests/quick_train_test.py
```

### 3. 开始训练
```bash
python train.py
```

## 详细说明

### 配置训练参数

在 `train.py` 中修改 `config` 字典来调整训练参数：

```python
config = {
    # 环境设置
    'img_height': 64,           # 图像高度
    'img_width': 64,            # 图像宽度
    'use_grayscale': True,      # 是否使用灰度图
    
    # 模型参数
    'latent_dim': 64,           # 潜在特征维度
    'num_embeddings': 16,       # 量化码本大小
    'gumbel_tau': 1.0,          # Gumbel-Softmax 温度
    
    # QAM 信道 (可选)
    'qam_m_ary': 16,            # QAM 调制阶数 (None 禁用)
    'qam_snr_db': 15.0,         # 信噪比 (dB)
    
    # PPO 超参数
    'learning_rate': 3e-4,      # 学习率
    'n_steps': 1024,            # 每次更新的步数
    'batch_size': 64,           # 小批次大小
    'n_epochs': 10,             # 每次更新的训练轮数
    'gamma': 0.99,              # 折扣因子
    'gae_lambda': 0.95,         # GAE lambda
    'clip_range': 0.2,          # PPO 裁剪范围
    
    # 损失权重
    'entropy_coef': 0.01,       # 策略熵系数
    'value_coef': 0.5,          # 价值损失系数
    'recon_coef': 1.0,          # 重构损失系数
    'quant_entropy_coef': 0.001, # 量化熵系数
    'max_grad_norm': 1.0,       # 梯度裁剪
    
    # 训练设置
    'total_timesteps': 200000,  # 总训练步数
    'log_interval': 1000,       # 日志间隔
    'save_interval': 10000,     # 模型保存间隔
    'eval_interval': 5000,      # 评估间隔
    
    # 输出路径
    'tensorboard_log': './logs/',
    'model_save_path': './model.pth',
    'reconstruction_save_dir': './reconstruction_images/'
}
```

### 监控训练进度

#### 1. 控制台输出
训练过程中会显示：
```
步骤 1000 | 回合: 15 | 平均奖励: 45.67 | 策略损失: 0.0234 | 价值损失: 12.34 | 重构损失: 0.567
```

#### 2. TensorBoard 日志
```bash
tensorboard --logdir=./logs/
```
然后在浏览器中打开 `http://localhost:6006` 查看训练曲线。

#### 3. 重构图像
训练过程中会定期保存重构图像到 `./reconstruction_images/` 目录，可以查看编码器的重构质量。

### 模型保存和加载

#### 保存的内容
```python
{
    'model_state_dict': model.state_dict(),
    'optimizer_state_dict': trainer.optimizer.state_dict(),
    'timestep': timestep,
    'config': config
}
```

#### 加载模型
```python
import torch
from networks.actor_critic import ActorCriticNetwork

# 加载检查点
checkpoint = torch.load('./model.pth')
config = checkpoint['config']

# 重建模型
model = ActorCriticNetwork(
    obs_shape=(1, 64, 64),
    action_dim=2,
    latent_dim=config['latent_dim'],
    num_embeddings=config['num_embeddings']
)

# 加载权重
model.load_state_dict(checkpoint['model_state_dict'])
```

## 自定义和扩展

### 1. 修改网络架构

在 `networks/actor_critic.py` 中修改 `ActorCriticNetwork` 类：

```python
# 添加更多层
self.policy_head = nn.Sequential(
    nn.Linear(latent_dim, 128),
    nn.ReLU(),
    nn.Linear(128, action_dim)
)
```

### 2. 添加新的损失项

在 `ppo_trainer.py` 的 `compute_ppo_loss` 方法中：

```python
# 添加新的损失
custom_loss = compute_custom_loss(z_q, obs)

# 更新总损失
total_loss = (
    policy_loss +
    self.value_coef * value_loss +
    self.entropy_coef * entropy_loss +
    self.recon_coef * recon_loss +
    self.quant_entropy_coef * quant_entropy_loss +
    self.custom_coef * custom_loss  # 新增
)
```

### 3. 支持新环境

创建新的环境包装器：

```python
class NewEnvWrapper:
    def __init__(self, env):
        self.env = env
        # 自定义预处理
    
    def step(self, action):
        # 自定义步骤逻辑
        return self.env.step(action)
```

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ModuleNotFoundError: No module named 'networks'
   ```
   **解决**: 确保在项目根目录运行脚本，或检查 Python 路径设置。

2. **CUDA 内存不足**
   ```
   RuntimeError: CUDA out of memory
   ```
   **解决**: 减小 `batch_size` 或 `n_steps`，或使用 CPU 训练。

3. **训练不收敛**
   - 检查学习率是否合适
   - 调整损失权重比例
   - 增加训练步数

4. **重构质量差**
   - 增加 `recon_coef` 权重
   - 检查编码器/解码器架构
   - 调整 `latent_dim` 大小

### 调试技巧

1. **使用小规模测试**
   ```bash
   python tests/quick_train_test.py
   ```

2. **检查梯度**
   ```python
   for name, param in model.named_parameters():
       if param.grad is not None:
           print(f"{name}: {param.grad.norm()}")
   ```

3. **可视化重构**
   定期检查 `./reconstruction_images/` 中的图像质量。

## 性能优化

### 1. 使用 GPU
确保 PyTorch 支持 CUDA，训练会自动使用 GPU。

### 2. 调整批次大小
根据显存大小调整 `batch_size` 和 `n_steps`。

### 3. 并行环境
可以扩展为支持多个并行环境以提高采样效率。

### 4. 混合精度训练
可以添加 `torch.cuda.amp` 支持以节省显存和加速训练。

## 结果分析

### 评估指标
- **平均奖励**: 智能体性能的主要指标
- **策略损失**: PPO 策略优化效果
- **价值损失**: 价值函数拟合质量
- **重构损失**: 编码器保真度
- **量化熵**: 码本利用率

### 成功标准
- CartPole 环境中平均奖励 > 450 表示训练成功
- 重构 MSE < 0.1 表示编码器质量良好
- 量化熵在合理范围内 (不过高也不过低)

通过遵循这个指南，您应该能够成功运行和自定义这个 PPO + 编码器训练系统。
