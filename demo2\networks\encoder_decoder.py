"""
图像处理的编码器和解码器模块。
重用现有的 DTJSCC_CIFAR10_Encoder 和 DTJSCC_CIFAR10_Decoder。
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple


class ImageEncoder(nn.Module):
    """现有 DTJSCC_CIFAR10_Encoder 的包装器"""
    
    def __init__(self, input_channels: int, feature_dim: int):
        super().__init__()
        # 在此处导入以避免循环导入
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from image_encoder import DTJSCC_CIFAR10_Encoder
        
        self.encoder = DTJSCC_CIFAR10_Encoder(
            input_channels=input_channels, 
            feature_dim=feature_dim
        )
        self.feature_dim = feature_dim
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: 形状为 (B, C, H, W) 的输入图像
        Returns:
            形状为 (B, feature_dim) 的编码特征
        """
        return self.encoder(x)


class ImageDecoder(nn.Module):
    """现有 DTJSCC_CIFAR10_Decoder 的包装器"""
    
    def __init__(self, feature_dim: int, output_channels: int):
        super().__init__()
        # 在此处导入以避免循环导入
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from image_encoder import DTJSCC_CIFAR10_Decoder
        
        self.decoder = DTJSCC_CIFAR10_Decoder(
            feature_dim=feature_dim,
            output_channels=output_channels
        )
        self.feature_dim = feature_dim
        self.output_channels = output_channels
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: 形状为 (B, feature_dim) 的编码特征
        Returns:
            形状为 (B, C, H, W) 的重构图像
        """
        return self.decoder(x)


class VectorQuantizer(nn.Module):
    """现有 GumbelSoftmaxQuantizer 的包装器"""
    
    def __init__(self, num_embeddings: int, embedding_dim: int, gumbel_tau: float = 1.0):
        super().__init__()
        # 在此处导入以避免循环导入
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from quantizer import GumbelSoftmaxQuantizer
        
        self.quantizer = GumbelSoftmaxQuantizer(
            num_embeddings=num_embeddings,
            embedding_dim=embedding_dim,
            gumbel_tau=gumbel_tau
        )
        self.num_embeddings = num_embeddings
        self.embedding_dim = embedding_dim
    
    def forward(self, x: torch.Tensor, mod=None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Args:
            x: 形状为 (B, embedding_dim) 的输入特征
            mod: 可选的 QAM 调制器
        Returns:
            (量化输出, 分布概率) 的元组
            - quantized_output: 形状 (B, embedding_dim)
            - dist_probs: 形状 (B, num_embeddings)
        """
        return self.quantizer.forward(x, mod=mod)
