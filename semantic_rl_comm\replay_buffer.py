import torch
import numpy as np
from typing import Dict, <PERSON><PERSON>, Optional, Union, List

class CustomReplayBuffer:
    def __init__(self,
                 buffer_size: int,
                 observation_space_env, # The environment's observation space (e.g., for O_t)
                 action_space_env,    # The environment's action space (for a_t)
                 # Dimensions for custom stored elements
                 X_dim: Union[int, Tuple[int, ...]], # Dimension of X_t (quantized vector)
                 Y_dim: Union[int, Tuple[int, ...]], # Dimension of Y_t (received vector)
                 belief_dim: Union[int, Tuple[int, ...]], # Dimension of b_t
                 rnn_hidden_dim: int, # Dimension of RNN hidden state H_rnn
                 num_rnn_layers: int = 1,
                 rnn_type: str = 'lstm', # To know if cell state 'c' is needed
                 # Optional: for dist_for_mi if it's stored
                 dist_mi_dim: Optional[Union[int, Tuple[int, ...]]] = None, 
                 device: str = 'cpu'):
        """
        Custom Replay Buffer.
        Args:
            buffer_size (int): Max number of transitions to store.
            observation_space_env: Original observation space from the environment (for O_t).
            action_space_env: Action space from the environment.
            X_dim: Dimension of the quantized vector X_t.
            Y_dim: Dimension of the received vector Y_t.
            belief_dim: Dimension of the belief state b_t.
            rnn_hidden_dim: Dimension of the RNN's hidden state.
            num_rnn_layers: Number of RNN layers.
            rnn_type: 'lstm' or 'gru'.
            dist_mi_dim: Optional dimension of distribution/logits for MI calculation.
            device (str): PyTorch device.
        """
        self.buffer_size = buffer_size
        self.device = device
        self.pos = 0
        self.full = False

        # Determine shapes for storage
        self.obs_shape = observation_space_env.shape
        self.action_shape = action_space_env.shape # Assuming discrete or simple Box for now
        
        # Storage for standard RL elements + custom ones
        # O_t (original observation from environment)
        self.observations_O = np.zeros((buffer_size, *self.obs_shape), dtype=observation_space_env.dtype)
        self.next_observations_O = np.zeros((buffer_size, *self.obs_shape), dtype=observation_space_env.dtype)
        
        # X_t (quantized vector from encoder)
        self.X_t_vectors = np.zeros((buffer_size, *([X_dim] if isinstance(X_dim, int) else X_dim)), dtype=np.float32)
        
        # Y_t (received vector after channel)
        # This is what BeliefEstimatorRNN takes as input.
        # It's also part of the observation for the SB3 SAC agent if using BeliefRnnFeaturesExtractor.
        self.Y_t_vectors = np.zeros((buffer_size, *([Y_dim] if isinstance(Y_dim, int) else Y_dim)), dtype=np.float32)
        # self.next_Y_t_vectors = np.zeros((buffer_size, *([Y_dim] if isinstance(Y_dim, int) else Y_dim)), dtype=np.float32) # If needed

        # Belief states b_t (output of BeliefEstimatorRNN)
        self.belief_states = np.zeros((buffer_size, *([belief_dim] if isinstance(belief_dim, int) else belief_dim)), dtype=np.float32)
        self.next_belief_states = np.zeros((buffer_size, *([belief_dim] if isinstance(belief_dim, int) else belief_dim)), dtype=np.float32)

        # RNN hidden states (h_t and c_t for LSTM)
        # Shape: [num_rnn_layers, rnn_hidden_dim] per sample
        self.hidden_states_h = np.zeros((buffer_size, num_rnn_layers, rnn_hidden_dim), dtype=np.float32)
        self.next_hidden_states_h = np.zeros((buffer_size, num_rnn_layers, rnn_hidden_dim), dtype=np.float32)
        
        self.rnn_type = rnn_type
        if self.rnn_type == 'lstm':
            self.cell_states_c = np.zeros((buffer_size, num_rnn_layers, rnn_hidden_dim), dtype=np.float32)
            self.next_cell_states_c = np.zeros((buffer_size, num_rnn_layers, rnn_hidden_dim), dtype=np.float32)
        else: # GRU or other
            self.cell_states_c = None # Not used
            self.next_cell_states_c = None

        # Actions, Rewards, Dones
        self.actions = np.zeros((buffer_size, *self.action_shape), dtype=action_space_env.dtype)
        self.rewards = np.zeros((buffer_size, 1), dtype=np.float32)
        self.dones = np.zeros((buffer_size, 1), dtype=np.float32) # Store as float for SB3 compatibility (0.0 or 1.0)

        # Optional: for dist_for_mi (logits from quantizer)
        self.store_dist_mi = dist_mi_dim is not None
        if self.store_dist_mi:
            self.dist_mi_values = np.zeros((buffer_size, *([dist_mi_dim] if isinstance(dist_mi_dim, int) else dist_mi_dim)), dtype=np.float32)
        else:
            self.dist_mi_values = None
            
        print(f"CustomReplayBuffer initialized with size {buffer_size} on device '{device}'.")
        print(f"  Storing O_t: shape {self.observations_O.shape}, dtype {self.observations_O.dtype}")
        print(f"  Storing X_t: shape {self.X_t_vectors.shape}")
        print(f"  Storing Y_t: shape {self.Y_t_vectors.shape}")
        print(f"  Storing b_t: shape {self.belief_states.shape}")
        print(f"  Storing h_t: shape {self.hidden_states_h.shape}")
        if self.rnn_type == 'lstm':
            print(f"  Storing c_t: shape {self.cell_states_c.shape}")
        if self.store_dist_mi:
            print(f"  Storing dist_mi: shape {self.dist_mi_values.shape}")


    def add(self,
            obs_O_t: np.ndarray,
            X_t: np.ndarray,
            Y_t: np.ndarray,
            belief_t: np.ndarray,
            h_t: np.ndarray, # Shape [num_layers, hidden_dim]
            c_t: Optional[np.ndarray], # Shape [num_layers, hidden_dim] or None
            action: np.ndarray,
            reward: float,
            next_obs_O_t: np.ndarray,
            next_belief_t: np.ndarray,
            next_h_t: np.ndarray, # Shape [num_layers, hidden_dim]
            next_c_t: Optional[np.ndarray], # Shape [num_layers, hidden_dim] or None
            done: bool,
            dist_mi: Optional[np.ndarray] = None):
        """
        Adds a transition to the buffer.
        h_t, c_t, next_h_t, next_c_t should be detached from graph and on CPU.
        They are typically [num_rnn_layers, rnn_hidden_dim] for a single sample.
        """
        self.observations_O[self.pos] = obs_O_t
        self.X_t_vectors[self.pos] = X_t
        self.Y_t_vectors[self.pos] = Y_t
        self.belief_states[self.pos] = belief_t
        self.hidden_states_h[self.pos] = h_t
        if self.rnn_type == 'lstm' and c_t is not None:
            self.cell_states_c[self.pos] = c_t
        
        self.actions[self.pos] = action
        self.rewards[self.pos] = reward
        self.dones[self.pos] = float(done) # Store as float

        self.next_observations_O[self.pos] = next_obs_O_t
        self.next_belief_states[self.pos] = next_belief_t
        self.next_hidden_states_h[self.pos] = next_h_t
        if self.rnn_type == 'lstm' and next_c_t is not None:
            self.next_cell_states_c[self.pos] = next_c_t

        if self.store_dist_mi and dist_mi is not None:
            self.dist_mi_values[self.pos] = dist_mi

        self.pos = (self.pos + 1) % self.buffer_size
        if self.pos == 0 and not self.full: # First time buffer is full
            self.full = True
            print("Replay buffer is now full.")

    def sample(self, batch_size: int) -> Dict[str, torch.Tensor]:
        """
        Samples a batch of transitions from the buffer.
        Returns:
            A dictionary of tensors, moved to self.device.
            Keys: "obs_O", "X_t", "Y_t", "belief_t", "h_t", "c_t" (if LSTM),
                  "actions", "rewards", "next_obs_O", "next_belief_t", 
                  "next_h_t", "next_c_t" (if LSTM), "dones", "dist_mi" (if stored).
        """
        current_size = self.buffer_size if self.full else self.pos
        if batch_size > current_size:
            # TODO: [明确当 batch_size 大于当前缓冲区大小时的采样行为，例如是报错、减少批次大小还是允许重复采样，并取消注释或修改实现]
            # print(f"Warning: Requested batch_size {batch_size} > current buffer size {current_size}. Sampling with replacement or reducing batch size might be needed.")
            # For now, let's sample with replacement if not enough, or raise error.
            # Or simply sample all available if batch_size > current_size.
            # This behavior should be chosen carefully.
            # For simplicity, sample 'current_size' if batch_size is too large.
            # Or, more commonly, sample with replacement.
            # Let's use sampling with replacement if not enough unique samples.
            indices = np.random.randint(0, current_size, size=batch_size)
        else:
            indices = np.random.choice(current_size, size=batch_size, replace=False)

        # Convert sampled numpy arrays to tensors and move to device
        batch = {
            "obs_O": torch.as_tensor(self.observations_O[indices], device=self.device).float(),
            "X_t": torch.as_tensor(self.X_t_vectors[indices], device=self.device).float(),
            "Y_t": torch.as_tensor(self.Y_t_vectors[indices], device=self.device).float(),
            "belief_t": torch.as_tensor(self.belief_states[indices], device=self.device).float(),
            "h_t": torch.as_tensor(self.hidden_states_h[indices], device=self.device).float(),
            "actions": torch.as_tensor(self.actions[indices], device=self.device), # Keep original dtype if discrete
            "rewards": torch.as_tensor(self.rewards[indices], device=self.device).float(),
            "next_obs_O": torch.as_tensor(self.next_observations_O[indices], device=self.device).float(),
            "next_belief_t": torch.as_tensor(self.next_belief_states[indices], device=self.device).float(),
            "next_h_t": torch.as_tensor(self.next_hidden_states_h[indices], device=self.device).float(),
            "dones": torch.as_tensor(self.dones[indices], device=self.device).float(),
        }
        if self.actions.dtype != np.float32 and self.actions.dtype != np.float64 : # e.g. int for discrete actions
             batch["actions"] = batch["actions"].long()


        if self.rnn_type == 'lstm':
            batch["c_t"] = torch.as_tensor(self.cell_states_c[indices], device=self.device).float()
            batch["next_c_t"] = torch.as_tensor(self.next_cell_states_c[indices], device=self.device).float()
        
        if self.store_dist_mi:
            batch["dist_mi"] = torch.as_tensor(self.dist_mi_values[indices], device=self.device).float()

        return batch

    def __len__(self) -> int:
        return self.buffer_size if self.full else self.pos


if __name__ == '__main__':
    # Example Usage
    from gym import spaces as gym_spaces # For older gym
    
    buf_size = 1000
    # Dummy env spaces
    obs_sp = gym_spaces.Box(low=0, high=255, shape=(2, 84, 84), dtype=np.uint8) # O_t (e.g. 2 stacked 84x84 frames)
    act_sp = gym_spaces.Discrete(2)

    # Dummy dimensions for custom elements
    x_dim_ex = 64       # D_codebook
    y_dim_ex = x_dim_ex # D_codebook
    b_dim_ex = 4        # CartPole belief state
    rnn_h_dim_ex = 128
    n_layers_ex = 1
    rnn_t_ex = 'lstm'
    dist_mi_dim_ex = 512 # K_codebook (logits for MI)
    dev = 'cpu'

    replay_buffer = CustomReplayBuffer(
        buffer_size=buf_size,
        observation_space_env=obs_sp,
        action_space_env=act_sp,
        X_dim=x_dim_ex,
        Y_dim=y_dim_ex,
        belief_dim=b_dim_ex,
        rnn_hidden_dim=rnn_h_dim_ex,
        num_rnn_layers=n_layers_ex,
        rnn_type=rnn_t_ex,
        dist_mi_dim=dist_mi_dim_ex,
        device=dev
    )

    # Add some dummy data
    for i in range(15): # Add 15 samples
        obs_o = obs_sp.sample()
        x_t_s = np.random.randn(x_dim_ex).astype(np.float32)
        y_t_s = np.random.randn(y_dim_ex).astype(np.float32)
        b_t_s = np.random.randn(b_dim_ex).astype(np.float32)
        h_t_s = np.random.randn(n_layers_ex, rnn_h_dim_ex).astype(np.float32)
        c_t_s = np.random.randn(n_layers_ex, rnn_h_dim_ex).astype(np.float32) if rnn_t_ex == 'lstm' else None
        act = act_sp.sample()
        rew = float(np.random.rand())
        next_obs_o = obs_sp.sample()
        next_b_t_s = np.random.randn(b_dim_ex).astype(np.float32)
        next_h_t_s = np.random.randn(n_layers_ex, rnn_h_dim_ex).astype(np.float32)
        next_c_t_s = np.random.randn(n_layers_ex, rnn_h_dim_ex).astype(np.float32) if rnn_t_ex == 'lstm' else None
        dn = bool(np.random.choice([True, False]))
        dist_mi_s = np.random.randn(dist_mi_dim_ex).astype(np.float32) if replay_buffer.store_dist_mi else None

        replay_buffer.add(
            obs_O_t=obs_o, X_t=x_t_s, Y_t=y_t_s, belief_t=b_t_s, h_t=h_t_s, c_t=c_t_s,
            action=act, reward=rew, next_obs_O_t=next_obs_o, next_belief_t=next_b_t_s,
            next_h_t=next_h_t_s, next_c_t=next_c_t_s, done=dn, dist_mi=dist_mi_s
        )
    
    print(f"\nBuffer current size: {len(replay_buffer)}")
    assert len(replay_buffer) == 15

    # Sample from buffer
    batch_sample = replay_buffer.sample(batch_size=4)
    print("\nSampled batch (first item of each tensor):")
    for key, tensor in batch_sample.items():
        print(f"  {key}: shape {tensor.shape}, first item example: {tensor[0].flatten()[:5]}...")
        assert tensor.shape[0] == 4 # Batch size
        assert tensor.device.type == dev
    
    print("\nCustomReplayBuffer test completed.")