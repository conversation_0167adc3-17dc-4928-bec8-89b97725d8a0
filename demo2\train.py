"""
PPO + 编码器训练的主训练脚本。
"""

import os

import gym
import matplotlib.pyplot as plt
import numpy as np
import torch
from channel import PSK  # QAM 调制

# 导入本地模块
from env_wrapper import CartPoleImageWrapper
from networks.actor_critic import ActorCriticNetwork
from ppo_trainer import PPOTrainer
from rollout_buffer import RolloutBuffer
from torch.utils.tensorboard import SummaryWriter
from tqdm import tqdm


def save_reconstruction_images(original_imgs, recon_imgs, step, save_dir):
    """保存原始图像和重构图像的对比图。"""
    os.makedirs(save_dir, exist_ok=True)

    # 将张量转换为 numpy
    if isinstance(original_imgs, torch.Tensor):
        original_imgs = original_imgs.detach().cpu().numpy()
    if isinstance(recon_imgs, torch.Tensor):
        recon_imgs = recon_imgs.detach().cpu().numpy()

    # 选择前4张图像进行对比
    n_images = min(4, original_imgs.shape[0])

    fig, axes = plt.subplots(2, n_images, figsize=(4 * n_images, 8))
    if n_images == 1:
        axes = axes.reshape(2, 1)

    for i in range(n_images):
        # 原始图像
        if original_imgs.shape[1] == 1:  # 灰度图
            axes[0, i].imshow(original_imgs[i, 0], cmap="gray")
        else:  # RGB
            axes[0, i].imshow(np.transpose(original_imgs[i], (1, 2, 0)))
        axes[0, i].set_title(f"原始图像 {i}")
        axes[0, i].axis("off")

        # 重构图像
        if recon_imgs.shape[1] == 1:  # 灰度图
            axes[1, i].imshow(recon_imgs[i, 0], cmap="gray")
        else:  # RGB
            axes[1, i].imshow(np.transpose(recon_imgs[i], (1, 2, 0)))
        axes[1, i].set_title(f"重构图像 {i}")
        axes[1, i].axis("off")

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f"reconstruction_step_{step}.png"))
    plt.close()


def main():
    # 配置
    config = {
        # 环境
        "img_height": 64,
        "img_width": 64,
        "use_grayscale": True,
        # 模型
        "latent_dim": 64,
        "num_embeddings": 16,
        "gumbel_tau": 1.0,
        # QAM 信道 (可选)
        "qam_m_ary": 16,
        "qam_snr_db": 15.0,
        # PPO
        "learning_rate": 3e-4,
        "n_steps": 1024,
        "batch_size": 64,
        "n_epochs": 10,
        "gamma": 0.99,
        "gae_lambda": 0.95,
        "clip_range": 0.2,
        "entropy_coef": 0.01,
        "value_coef": 0.5,
        "recon_coef": 1.0,
        "quant_entropy_coef": 0.001,
        "max_grad_norm": 1.0,
        # 训练
        "total_timesteps": 200000,
        "log_interval": 1000,
        "save_interval": 10000,
        "eval_interval": 5000,
        # 日志
        "tensorboard_log": "./logs/",
        "model_save_path": "./model.pth",
        "reconstruction_save_dir": "./reconstruction_images/",
    }

    # 设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    print(f"使用设备: {device}")

    # 验证配置
    if config["qam_m_ary"] is not None and config["num_embeddings"] > config["qam_m_ary"]:
        raise ValueError(f"num_embeddings ({config['num_embeddings']}) 不能超过 qam_m_ary ({config['qam_m_ary']})")

    # 设置环境
    env = gym.make("CartPole-v1", render_mode="rgb_array")
    env = CartPoleImageWrapper(
        env, height=config["img_height"], width=config["img_width"], grayscale=config["use_grayscale"]
    )

    obs_shape = env.observation_space.shape  # (C, H, W)
    action_dim = env.action_space.n

    print("环境: CartPole-v1")
    print(f"观测形状: {obs_shape}")
    print(f"动作维度: {action_dim}")

    # 设置 QAM 调制器 (可选)
    qam_modem = None
    if config["qam_m_ary"] is not None and config["qam_snr_db"] is not None:
        qam_modem = PSK(M=config["qam_m_ary"], SNR=config["qam_snr_db"])
        print(f"QAM 信道: M-ary={config['qam_m_ary']}, SNR={config['qam_snr_db']}dB")
    else:
        print("QAM 信道: 禁用")

    # 创建模型
    model = ActorCriticNetwork(
        obs_shape=obs_shape,
        action_dim=action_dim,
        latent_dim=config["latent_dim"],
        num_embeddings=config["num_embeddings"],
        gumbel_tau=config["gumbel_tau"],
        qam_modem=qam_modem,
    ).to(device)

    print(f"模型参数: {sum(p.numel() for p in model.parameters()):,}")

    # 创建训练器
    trainer = PPOTrainer(
        model=model,
        learning_rate=config["learning_rate"],
        clip_range=config["clip_range"],
        value_coef=config["value_coef"],
        entropy_coef=config["entropy_coef"],
        recon_coef=config["recon_coef"],
        quant_entropy_coef=config["quant_entropy_coef"],
        max_grad_norm=config["max_grad_norm"],
        device=device,
    )

    # 创建轨迹缓冲区
    rollout_buffer = RolloutBuffer(buffer_size=config["n_steps"], obs_shape=obs_shape, device=device)

    # 设置日志
    os.makedirs(config["tensorboard_log"], exist_ok=True)
    os.makedirs(config["reconstruction_save_dir"], exist_ok=True)
    writer = SummaryWriter(config["tensorboard_log"])

    # 训练循环
    obs = env.reset()
    episode_rewards = []
    current_episode_reward = 0
    timestep = 0
    episode_count = 0
    update_count = 0

    print(f"\n开始训练 {config['total_timesteps']} 步...")
    print("=" * 50)

    # 创建主进度条
    main_pbar = tqdm(total=config["total_timesteps"], desc="训练进度", unit="步", ncols=100, position=0)

    while timestep < config["total_timesteps"]:
        # 收集轨迹数据
        rollout_pbar = tqdm(
            total=config["n_steps"],
            desc=f"收集数据 (更新 {update_count + 1})",
            unit="步",
            ncols=80,
            position=1,
            leave=False,
        )

        steps_collected = 0
        for step in range(config["n_steps"]):
            # 将观测转换为张量
            obs_tensor = torch.tensor(obs, dtype=torch.float32, device=device).unsqueeze(0)

            # 从策略获取动作
            action, log_prob, value = model.act(obs_tensor)

            # 在环境中执行步骤
            next_obs, reward, done, _ = env.step(action)

            # 存储转换
            rollout_buffer.add(
                obs=obs, action=action, log_prob=log_prob.item(), reward=reward, done=done, value=value.item()
            )

            # 更新状态
            obs = next_obs
            current_episode_reward += reward
            timestep += 1
            steps_collected += 1

            # 更新进度条
            main_pbar.update(1)
            rollout_pbar.update(1)

            # 更新进度条描述
            avg_reward = np.mean(episode_rewards[-10:]) if episode_rewards else 0
            main_pbar.set_postfix(
                {"回合": episode_count, "平均奖励": f"{avg_reward:.1f}", "当前奖励": f"{current_episode_reward:.1f}"}
            )

            # 处理回合结束
            if done:
                episode_rewards.append(current_episode_reward)
                current_episode_reward = 0
                episode_count += 1
                obs = env.reset()

            # 检查是否收集了足够的数据
            if rollout_buffer.is_full():
                break

        rollout_pbar.close()

        # 计算下一个价值用于自举
        if not done:
            obs_tensor = torch.tensor(obs, dtype=torch.float32, device=device).unsqueeze(0)
            _, _, next_value = model.act(obs_tensor)
            next_value = next_value.item()
        else:
            next_value = 0.0

        # 执行 PPO 更新
        tqdm.write(f"执行 PPO 更新 {update_count + 1}...")
        update_stats = trainer.update(
            rollout_buffer=rollout_buffer,
            n_epochs=config["n_epochs"],
            batch_size=config["batch_size"],
            gamma=config["gamma"],
            gae_lambda=config["gae_lambda"],
            next_value=next_value,
        )

        update_count += 1

        # 清空缓冲区以进行下一次轨迹收集
        rollout_buffer.clear()

        # 日志记录
        if timestep % config["log_interval"] == 0:
            avg_reward = np.mean(episode_rewards[-10:]) if episode_rewards else 0
            tqdm.write(
                f"步骤 {timestep:6d} | 回合: {episode_count:4d} | "
                f"平均奖励: {avg_reward:6.2f} | "
                f"策略损失: {update_stats['policy_loss']:.4f} | "
                f"价值损失: {update_stats['value_loss']:.4f} | "
                f"重构损失: {update_stats['recon_loss']:.4f}"
            )

            # TensorBoard 日志
            writer.add_scalar("Environment/Average_Reward", avg_reward, timestep)
            writer.add_scalar("Environment/Episodes", episode_count, timestep)
            for key, value in update_stats.items():
                writer.add_scalar(f"Training/{key}", value, timestep)

        # 保存重构图像
        if timestep % config["eval_interval"] == 0:
            tqdm.write(f"保存重构图像 (步骤 {timestep})...")
            with torch.no_grad():
                # 获取一批观测用于重构
                sample_obs = torch.tensor(np.array([env.reset() for _ in range(4)]), dtype=torch.float32, device=device)
                recon_imgs = model.reconstruct(sample_obs)
                save_reconstruction_images(sample_obs, recon_imgs, timestep, config["reconstruction_save_dir"])

        # 保存模型
        if timestep % config["save_interval"] == 0:
            tqdm.write(f"保存模型 (步骤 {timestep})...")
            torch.save(
                {
                    "model_state_dict": model.state_dict(),
                    "optimizer_state_dict": trainer.optimizer.state_dict(),
                    "timestep": timestep,
                    "config": config,
                },
                config["model_save_path"],
            )

    # 关闭进度条
    main_pbar.close()

    # 最终保存
    torch.save(
        {
            "model_state_dict": model.state_dict(),
            "optimizer_state_dict": trainer.optimizer.state_dict(),
            "timestep": timestep,
            "config": config,
        },
        config["model_save_path"],
    )

    print(f"\n训练完成！模型已保存到 {config['model_save_path']}")
    print(f"TensorBoard 日志: {config['tensorboard_log']}")
    print(f"重构图像: {config['reconstruction_save_dir']}")

    env.close()
    writer.close()


if __name__ == "__main__":
    main()
