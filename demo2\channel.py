import numpy as np
import torch


class PSK:
    def __init__(self, M, SNR):
        """
        Args:
            M (int): Number of QAM constellation points (e.g., 4, 16, 64). Must be a perfect square.
            PSNR (float): Peak Signal to Noise Ratio in dB. If None, no noise is added.
        """
        if not np.sqrt(M).is_integer():
            raise ValueError(f"M must be a perfect square for square QAM. Got M={M}")
        self.M = M
        self.p = 1  # 功率
        self.delta = self._compute_noise(SNR)
        self.constellation = self._build()

    def _compute_noise(self, SNR):
        """
        Compute the noise standard deviation based on SNR.
        Args:
            SNR (float): Peak Signal to Noise Ratio in dB.
        Returns:
            float: Noise standard deviation.
        """
        delta_2 = self.p / torch.pow(torch.tensor(10), SNR / 10).float()
        return torch.sqrt(delta_2 / 2)

    def _build(self):
        constellation = torch.ones(self.M, 2)
        for i in range(self.M):
            constellation[i, 0] = np.cos(2 * np.pi * i / self.M)
            constellation[i, 1] = np.sin(2 * np.pi * i / self.M)
        return constellation

    def awgn(self, X: torch.Tensor) -> torch.Tensor:
        X += self.delta * torch.randn_like(X)
        return X

    def modulate(self, z: torch.Tensor) -> torch.Tensor:
        """
        Modulates a batch of integer indices to QAM constellation points.
        Args:
            z (torch.Tensor): Batch of integer indices. Shape (batch_size,).
                              Values must be between 0 and M-1.
        Returns:
            torch.Tensor: Modulated QAM symbols. Shape (batch_size, 2).
        """
        m = z.shape[0]
        X = torch.ones(int(m), 2)
        for i in range(m):
            X[i] = self.constellation[int(z[i])]
        return X

    def demodulate(self, X: torch.Tensor) -> torch.Tensor:
        """
        Demodulates a batch of QAM symbols to integer indices.
        Args:
            X (torch.Tensor): Batch of QAM symbols. Shape (batch_size, 2).
        Returns:
            torch.Tensor: Demodulated integer indices. Shape (batch_size,).
        """
        inner = np.matmul(X, self.constellation.T)
        return np.argmax(inner, axis=1)
