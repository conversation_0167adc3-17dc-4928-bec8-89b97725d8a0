import torch
import torch.nn as nn
import torch.nn.functional as F

from ..channel.channel_models import Channel

# Assuming image_encoder.py and quantizer.py are in the same directory
from .image_encoder import ImageCNNEncoder
from .quantizer import GumbelSoftmaxQuantizer, _entr  # Make _entr available if needed directly here


class EncoderAgent(nn.Module):
    def __init__(
        self,
        # ImageEncoder params
        input_channels,
        cnn_feature_dim,  # Output dim of CNN, input to pre_quant_fc in Quantizer
        # Quantizer params
        num_embeddings_K,
        embedding_dim_D,
        snr_db_train=None,
        gumbel_tau=1.0,
        # EncoderAgent specific loss params
        lambda_mi=0.1,
        gamma_enc=0.99,
        device="cpu",
    ):
        super().__init__()

        self.image_encoder = ImageCNNEncoder(
            input_channels=input_channels,
            feature_dim=cnn_feature_dim,  # This is z_raw's dim
        ).to(device)

        self.quantizer = GumbelSoftmaxQuantizer(
            input_feature_dim=cnn_feature_dim,  # z_raw_dim is input to pre_quant_fc
            num_embeddings_K=num_embeddings_K,
            embedding_dim_D=embedding_dim_D,
            gumbel_tau=gumbel_tau,
        ).to(device)

        self.channel_model = Channel(num_embeddings_K, snr_db_train)

        self.lambda_mi = lambda_mi
        self.gamma_enc = gamma_enc
        self.device = device

    def encode_and_channel(self, obs_images):
        """
        Encodes observation images to quantized codebook vectors.
        Args:
            obs_images (torch.Tensor): Batch of observation images.
                                       Shape: [batch_size, input_channels, H, W].
        Returns:
            Y_t (torch.Tensor): Batch of received signals.
                                Shape: [batch_size, embedding_dim_D].
            logits (torch.Tensor): Batch of logits from the quantizer.
                                   Shape: [batch_size, num_embeddings_K].
        """
        z_raw = self.image_encoder.forward(obs_images)
        Y_t, logits = self.quantizer.forward(z_raw, codebook_mask=None, channel=self.channel_model)
        return Y_t, logits

    def _entr(dist):
        dist = dist + 1e-7
        en_z_M = torch.mul(-1 * dist, torch.log(dist))
        en_z = torch.sum(torch.sum(en_z_M, dim=-1), dim=-1) / en_z_M.size(-2)
        return en_z

    def compute_loss(self, rewards_batch, logits_batch, lam):
        """
        Computes the encoder's loss: L_encoder = Σ γ_enc^t [r_t + λ * MI_term]
        Args:
            rewards_batch (torch.Tensor): Batch of rewards r_t. Shape [batch_size, 1] or [batch_size].
            logits_batch (torch.Tensor): Logits from quantizer for X_t. Shape [batch_size, num_embeddings_K].
            X_batch (torch.Tensor): Quantized codebook vectors X_t. Shape [batch_size, embedding_dim_D].
            Y_batch (torch.Tensor): Received (noisy) codebook vectors Y_t. Shape [batch_size, embedding_dim_D].
            O_batch_or_Ze_batch (torch.Tensor): Original observations O_t or features z_e(O_t) for MI calculation.
                                                Shape depends on definition.
        Returns:
            torch.Tensor: Scalar loss value for the encoder.
        """
        # Ensure rewards are [batch_size, 1] for broadcasting if needed
        if rewards_batch.ndim == 1:
            rewards_batch = rewards_batch.unsqueeze(-1)

        loss_recon = -torch.mean(rewards_batch)
        loss_entr = _entr(dist=logits_batch)  # Assuming _entr is a function to compute entropy
        loss = loss_recon - lam * loss_entr

        return loss
