"""
PPO 实现的测试脚本。
"""

import os
import sys

import gym
import numpy as np
import torch

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from env_wrapper import CartPoleImageWrapper
from networks.actor_critic import ActorCriticNetwork
from ppo_trainer import PPOTrainer
from rollout_buffer import RolloutBuffer


def test_model_creation():
    """测试模型创建是否无错误。"""
    print("测试模型创建...")

    # 设置环境以获取观测形状
    env = gym.make("CartPole-v1", render_mode="rgb_array")
    env = CartPoleImageWrapper(env, height=64, width=64, grayscale=True)

    obs_shape = env.observation_space.shape  # (C, H, W)
    action_dim = env.action_space.n

    print(f"观测形状: {obs_shape}")
    print(f"动作维度: {action_dim}")

    # 创建模型
    model = ActorCriticNetwork(
        obs_shape=obs_shape, action_dim=action_dim, latent_dim=64, num_embeddings=16, gumbel_tau=1.0
    )

    print("模型创建成功！")
    print(f"总参数: {sum(p.numel() for p in model.parameters()):,}")

    env.close()
    return model, obs_shape, action_dim


def test_forward_pass(model, obs_shape):
    """测试模型的前向传播。"""
    print("\n测试前向传播...")

    # 创建虚拟观测
    batch_size = 4
    obs = torch.randn(batch_size, *obs_shape)

    # 前向传播
    logits, values, z_q, dist_probs = model(obs)

    print(f"输入形状: {obs.shape}")
    print(f"Logits 形状: {logits.shape}")
    print(f"Values 形状: {values.shape}")
    print(f"量化特征形状: {z_q.shape}")
    print(f"分布概率形状: {dist_probs.shape}")

    # 测试动作采样
    action, log_prob, value = model.act(obs[0])
    print(f"采样动作: {action}")
    print(f"对数概率: {log_prob}")
    print(f"价值估计: {value}")

    # 测试重构
    recon = model.reconstruct(obs)
    print(f"重构形状: {recon.shape}")

    print("前向传播测试通过！")


def test_trainer_creation(model):
    """测试训练器创建和损失计算。"""
    print("\n测试训练器创建...")

    device = torch.device("cpu")
    trainer = PPOTrainer(model=model, learning_rate=3e-4, device=device)

    print("训练器创建成功！")

    # 用虚拟数据测试损失计算
    batch_size = 8
    obs_shape = model.obs_shape
    action_dim = model.action_dim

    obs = torch.randn(batch_size, *obs_shape)
    actions = torch.randint(0, action_dim, (batch_size,))
    old_log_probs = torch.randn(batch_size)
    advantages = torch.randn(batch_size)
    returns = torch.randn(batch_size)

    total_loss, loss_dict = trainer.compute_ppo_loss(obs, actions, old_log_probs, advantages, returns)

    print(f"总损失: {total_loss.item():.4f}")
    for key, value in loss_dict.items():
        print(f"{key}: {value.item():.4f}")

    print("训练器测试通过！")


def test_rollout_buffer(obs_shape):
    """测试轨迹缓冲区功能。"""
    print("\n测试轨迹缓冲区...")

    device = torch.device("cpu")
    buffer = RolloutBuffer(buffer_size=10, obs_shape=obs_shape, device=device)

    # 添加一些虚拟数据
    for i in range(5):
        obs = np.random.randn(*obs_shape)
        buffer.add(obs=obs, action=i % 2, log_prob=np.random.randn(), reward=1.0, done=False, value=np.random.randn())

    print(f"缓冲区长度: {len(buffer)}")

    # 测试数据处理
    obs_tensor, actions, old_log_probs, advantages, returns = buffer.get_data()

    print("处理后的数据形状:")
    print(f"  观测: {obs_tensor.shape}")
    print(f"  动作: {actions.shape}")
    print(f"  旧对数概率: {old_log_probs.shape}")
    print(f"  优势: {advantages.shape}")
    print(f"  回报: {returns.shape}")

    buffer.clear()
    print(f"清空后缓冲区长度: {len(buffer)}")

    print("轨迹缓冲区测试通过！")


def test_environment_interaction():
    """测试与环境的交互。"""
    print("\n测试环境交互...")

    # 设置环境
    env = gym.make("CartPole-v1", render_mode="rgb_array")
    env = CartPoleImageWrapper(env, height=64, width=64, grayscale=True)

    obs_shape = env.observation_space.shape
    action_dim = env.action_space.n

    # 创建模型
    model = ActorCriticNetwork(obs_shape=obs_shape, action_dim=action_dim, latent_dim=64, num_embeddings=16)

    # 测试几个步骤
    obs = env.reset()
    total_reward = 0

    for step in range(10):
        obs_tensor = torch.tensor(obs, dtype=torch.float32).unsqueeze(0)
        action, log_prob, value = model.act(obs_tensor)

        next_obs, reward, done, info = env.step(action)
        total_reward += reward

        print(f"步骤 {step}: 动作={action}, 奖励={reward}, 价值={value:.3f}")

        if done:
            obs = env.reset()
            print(f"回合结束。总奖励: {total_reward}")
            total_reward = 0
        else:
            obs = next_obs

    env.close()
    print("环境交互测试通过！")


def main():
    """运行所有测试。"""
    print("=" * 50)
    print("测试 PPO 实现")
    print("=" * 50)

    try:
        # 测试模型创建
        model, obs_shape, action_dim = test_model_creation()

        # 测试前向传播
        test_forward_pass(model, obs_shape)

        # 测试训练器
        test_trainer_creation(model)

        # 测试轨迹缓冲区
        test_rollout_buffer(obs_shape)

        # 测试环境交互
        test_environment_interaction()

        print("\n" + "=" * 50)
        print("所有测试成功通过！")
        print("=" * 50)

    except Exception as e:
        print(f"\n测试失败，错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
