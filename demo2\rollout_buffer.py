"""
Rollout buffer for storing and processing PPO training data.
"""

import numpy as np
import torch
from typing import List, Tu<PERSON>, Optional


class RolloutBuffer:
    """
    Buffer for storing rollout data for PPO training.
    """
    
    def __init__(self, buffer_size: int, obs_shape: Tuple[int, int, int], device: torch.device):
        """
        Args:
            buffer_size: Maximum number of steps to store
            obs_shape: Shape of observations (C, H, W)
            device: Device to store tensors on
        """
        self.buffer_size = buffer_size
        self.obs_shape = obs_shape
        self.device = device
        
        # Storage arrays
        self.observations = []
        self.actions = []
        self.log_probs = []
        self.rewards = []
        self.dones = []
        self.values = []
        
        self.ptr = 0
        self.full = False
    
    def add(
        self,
        obs: np.ndarray,
        action: int,
        log_prob: float,
        reward: float,
        done: bool,
        value: float
    ):
        """Add a single step to the buffer."""
        self.observations.append(obs)
        self.actions.append(action)
        self.log_probs.append(log_prob)
        self.rewards.append(reward)
        self.dones.append(done)
        self.values.append(value)
        
        self.ptr += 1
        if self.ptr >= self.buffer_size:
            self.full = True
    
    def get_data(self, gamma: float = 0.99, gae_lambda: float = 0.95, next_value: float = 0.0) -> Tuple[torch.Tensor, ...]:
        """
        Process stored data and compute advantages and returns.
        
        Args:
            gamma: Discount factor
            gae_lambda: GAE lambda parameter
            next_value: Value of the next state (for bootstrapping)
        
        Returns:
            Tuple of tensors: (obs, actions, old_log_probs, advantages, returns)
        """
        if len(self.observations) == 0:
            raise ValueError("Buffer is empty")
        
        # Convert to numpy arrays
        rewards = np.array(self.rewards)
        values = np.array(self.values)
        dones = np.array(self.dones)
        
        # Compute advantages using GAE
        advantages = np.zeros_like(rewards)
        returns = np.zeros_like(rewards)
        
        gae = 0
        for i in reversed(range(len(rewards))):
            if i == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[i]
                next_val = next_value
            else:
                next_non_terminal = 1.0 - dones[i]
                next_val = values[i + 1]
            
            delta = rewards[i] + gamma * next_val * next_non_terminal - values[i]
            gae = delta + gamma * gae_lambda * next_non_terminal * gae
            advantages[i] = gae
            returns[i] = gae + values[i]
        
        # Convert to tensors
        obs_tensor = torch.tensor(np.array(self.observations), dtype=torch.float32, device=self.device)
        actions_tensor = torch.tensor(self.actions, dtype=torch.long, device=self.device)
        old_log_probs_tensor = torch.tensor(self.log_probs, dtype=torch.float32, device=self.device)
        advantages_tensor = torch.tensor(advantages, dtype=torch.float32, device=self.device)
        returns_tensor = torch.tensor(returns, dtype=torch.float32, device=self.device)
        
        # Normalize advantages
        advantages_tensor = (advantages_tensor - advantages_tensor.mean()) / (advantages_tensor.std() + 1e-8)
        
        return obs_tensor, actions_tensor, old_log_probs_tensor, advantages_tensor, returns_tensor
    
    def clear(self):
        """Clear the buffer."""
        self.observations.clear()
        self.actions.clear()
        self.log_probs.clear()
        self.rewards.clear()
        self.dones.clear()
        self.values.clear()
        self.ptr = 0
        self.full = False
    
    def __len__(self):
        return len(self.observations)
    
    def is_full(self):
        return self.full or len(self.observations) >= self.buffer_size


class MiniBatchSampler:
    """
    Utility class for sampling mini-batches from rollout data.
    """
    
    def __init__(self, batch_size: int, data_size: int):
        self.batch_size = batch_size
        self.data_size = data_size
    
    def __iter__(self):
        """Generate mini-batch indices."""
        indices = np.arange(self.data_size)
        np.random.shuffle(indices)
        
        for start in range(0, self.data_size, self.batch_size):
            end = min(start + self.batch_size, self.data_size)
            yield indices[start:end]
