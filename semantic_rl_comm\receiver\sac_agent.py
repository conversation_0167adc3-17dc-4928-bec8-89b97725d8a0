from typing import Dict, Tuple

import gym
import torch
import torch.nn as nn
from gym import spaces
from stable_baselines3.common.torch_layers import BaseFeaturesExtractor

# Assuming BeliefEstimatorRNN is in the same directory
from .belief_estimator import BeliefEstimatorRNN


class BeliefRnnFeaturesExtractor(BaseFeaturesExtractor):
    """
    Custom features extractor for SAC that uses a BeliefEstimatorRNN.
    It expects observations to be a dictionary containing:
    - 'y': the received signal Y_t
    - 'h': the previous hidden state of the RNN
    - 'c': the previous cell state of the RNN (if LSTM)
    It outputs the belief state b_t as features.
    """

    def __init__(
        self,
        observation_space: gym.spaces.Dict,
        features_dim: int,  # This is the belief_dim
        # BeliefEstimatorRNN specific parameters
        rnn_input_dim_Y: int,  # Should match observation_space['y'].shape[0]
        rnn_hidden_dim: int,
        rnn_type: str = "lstm",
        num_rnn_layers: int = 1,
        device: str = "cpu",
    ):
        """
        Args:
            observation_space (gym.spaces.Dict): Observation space, must be a Dict
                                                 containing 'y', 'h', and optionally 'c'.
            features_dim (int): Dimension of the output features (belief_state dimension).
            rnn_input_dim_Y (int): Dimension of Y_t.
            rnn_hidden_dim (int): Hidden dimension for the RNN.
            rnn_type (str): 'lstm' or 'gru'.
            num_rnn_layers (int): Number of RNN layers.
            device (str): PyTorch device.
        """
        super().__init__(observation_space, features_dim=features_dim)

        self.belief_estimator = BeliefEstimatorRNN(
            input_dim_Y=rnn_input_dim_Y,
            rnn_hidden_dim=rnn_hidden_dim,
            belief_dim=features_dim,  # The output of belief_estimator is the feature
            rnn_type=rnn_type,
            num_rnn_layers=num_rnn_layers,
            device=device,
        ).to(device)

        self.device = device
        # self._features_dim is set by super class to features_dim

    def forward(self, observations: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Args:
            observations (Dict[str, torch.Tensor]): A dictionary of tensors,
                expected to contain 'y', 'h', and 'c' (if LSTM).
                'y': shape (batch_size, rnn_input_dim_Y)
                'h': shape (batch_size, num_rnn_layers, rnn_hidden_dim) - Note: SB3 might flatten this.
                     Our BeliefEstimatorRNN expects (num_rnn_layers, batch_size, rnn_hidden_dim).
                'c': shape (batch_size, num_rnn_layers, rnn_hidden_dim) - Same shape convention as 'h'.
        Returns:
            torch.Tensor: The belief state b_t. Shape (batch_size, self._features_dim).
        """
        Y_t = observations["y"].to(self.device)
        h_prev_flat = observations.get("h", None)
        c_prev_flat = observations.get("c", None)

        batch_size = Y_t.size(0)

        h_prev, c_prev = None, None

        if h_prev_flat is not None:
            # Reshape h_prev from [batch_size, num_layers * hidden_dim] or [batch_size, num_layers, hidden_dim]
            # to [num_layers, batch_size, hidden_dim] as expected by PyTorch RNNs.
            # SB3 flattens Dict observation spaces by default for MLPs.
            # We need to know the original shape or ensure it's passed correctly.
            # For now, assume it might come in as [batch_size, num_layers, rnn_hidden_dim]
            # or needs to be initialized if not present/correctly shaped.
            if h_prev_flat.ndim == 3 and h_prev_flat.shape[0] == batch_size:  # [batch, num_layers, hidden]
                h_prev = h_prev_flat.permute(1, 0, 2).contiguous().to(self.device)
            # else: could try to reshape from flattened, or initialize
            # For simplicity, if not in [B,L,H] or [L,B,H], initialize.
            # TODO: [确认并细化RNN隐藏状态h_prev和c_prev的形状处理逻辑，确保与SB3传递Dict观测值的方式兼容，特别是在处理扁平化观测或从replay buffer读取时] This part might need refinement based on how SB3 handles Dict obs with recurrent states
            # when NOT using its own recurrent policies.
            # A common pattern is that the replay buffer stores them correctly, and they are fed as is.

        if self.belief_estimator.rnn_type == "lstm" and c_prev_flat is not None:
            if c_prev_flat.ndim == 3 and c_prev_flat.shape[0] == batch_size:  # [batch, num_layers, hidden]
                c_prev = c_prev_flat.permute(1, 0, 2).contiguous().to(self.device)

        # If h_prev or c_prev are still None, BeliefEstimatorRNN's forward will init them.
        # This is okay for the first step of an episode.
        # For subsequent steps, they should ideally be passed from the replay buffer.

        belief_state, _h_next, _c_next = self.belief_estimator(Y_t, h_prev, c_prev)

        return belief_state


import numpy as np

if __name__ == "__main__":
    # Example Usage (Illustrative)
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # Parameters for the example
    batch_s = 4
    y_dim = 64  # Dimension of Y_t (e.g., D_codebook)
    belief_out_dim = 4  # Dimension of belief state b_t
    rnn_h_dim = 128
    n_rnn_layers = 1
    rnn_t = "lstm"

    # 1. Define the observation space (as the environment would)
    # Note: SB3 flattens Dict spaces for non-image spaces by default when passing to MLP.
    # For custom extractors, it passes the Dict.
    obs_space_dict = spaces.Dict(
        {
            "y": spaces.Box(low=-np.inf, high=np.inf, shape=(y_dim,), dtype=np.float32),
            "h": spaces.Box(low=-np.inf, high=np.inf, shape=(n_rnn_layers, rnn_h_dim), dtype=np.float32),
            # 'c' only if LSTM, for GRU it might be omitted or be a dummy space
            "c": spaces.Box(low=-np.inf, high=np.inf, shape=(n_rnn_layers, rnn_h_dim), dtype=np.float32),
        }
    )
    if rnn_t == "gru":
        obs_space_dict = spaces.Dict(
            {
                "y": spaces.Box(low=-np.inf, high=np.inf, shape=(y_dim,), dtype=np.float32),
                "h": spaces.Box(low=-np.inf, high=np.inf, shape=(n_rnn_layers, rnn_h_dim), dtype=np.float32),
            }
        )

    # 2. Instantiate the custom features extractor
    custom_extractor = BeliefRnnFeaturesExtractor(
        observation_space=obs_space_dict,
        features_dim=belief_out_dim,
        rnn_input_dim_Y=y_dim,
        rnn_hidden_dim=rnn_h_dim,
        rnn_type=rnn_t,
        num_rnn_layers=n_rnn_layers,
        device=device,
    )
    print("Custom BeliefRnnFeaturesExtractor:")
    print(custom_extractor)

    # 3. Create dummy observations (as SB3 would pass them)
    # SB3 passes a dictionary of tensors
    dummy_obs_y = torch.randn(batch_s, y_dim).to(device)
    dummy_obs_h = torch.randn(batch_s, n_rnn_layers, rnn_h_dim).to(device)  # Shape [B, L, H]

    dummy_observations_batch = {"y": dummy_obs_y, "h": dummy_obs_h}
    if rnn_t == "lstm":
        dummy_obs_c = torch.randn(batch_s, n_rnn_layers, rnn_h_dim).to(device)
        dummy_observations_batch["c"] = dummy_obs_c

    print(f"\nInput Y_t shape: {dummy_observations_batch['y'].shape}")
    print(f"Input h_prev shape (from batch): {dummy_observations_batch['h'].shape}")
    if rnn_t == "lstm":
        print(f"Input c_prev shape (from batch): {dummy_observations_batch['c'].shape}")

    # 4. Test the forward pass
    extracted_features = custom_extractor(dummy_observations_batch)
    print(f"Extracted features (belief_state) shape: {extracted_features.shape}")  # Expected: [batch_s, belief_out_dim]
    assert extracted_features.shape == (batch_s, belief_out_dim)
    print("BeliefRnnFeaturesExtractor test completed.")
