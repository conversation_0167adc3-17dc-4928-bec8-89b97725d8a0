import gym
from gym import spaces
import numpy as np
# 可能需要: from gymnasium.wrappers import PixelObservationWrapper (如果决定使用)
# 可能需要: import cv2 # 用于图像处理

class CustomCartpoleImgEnv(gym.Env):
    metadata = {'render_modes': ['rgb_array'], 'render_fps': 30}

    def __init__(self, frame_stack=2, screen_width=600, screen_height=400):
        super().__init__()
        self.env = gym.make("CartPole-v1", render_mode="rgb_array")
        self.frame_stack = frame_stack
        self.screen_width = screen_width
        self.screen_height = screen_height

        # 定义动作空间 (与原始CartPole相同)
        self.action_space = self.env.action_space

        # 定义观测空间 (图像)
        # 例如: C x H x W (通道数 x 高度 x 宽度)
        # 通道数将是 frame_stack * num_channels_per_frame (例如，灰度图是1，RGB是3)
        # 这里假设我们转换为灰度图并堆叠
        self.channels_per_frame = 1 # 假设灰度图
        self.observation_shape = (self.channels_per_frame * self.frame_stack, self.screen_height, self.screen_width)
        self.observation_space = spaces.Box(
            low=0, high=255, shape=self.observation_shape, dtype=np.uint8
        )

        self.stacked_frames = np.zeros(self.observation_shape, dtype=np.uint8)
        self.viewer = None # for rendering

    def _get_observation(self):
        # 获取原始环境的渲染图像
        rgb_array = self.env.render() #  mode='rgb_array' 已在make中指定

        if rgb_array is not None:
            # 将图片转化为灰度图
            gray_frame = np.mean(rgb_array, axis=2).astype(np.uint8)
            # 将图片缩放到屏幕大小
            if gray_frame.shape[0] != self.screen_height or gray_frame.shape[1] != self.screen_width:
                resized_frame = np.zeros((self.screen_height, self.screen_width), dtype=np.uint8)
                min_h = min(gray_frame.shape[0], self.screen_height)
                min_w = min(gray_frame.shape[1], self.screen_width)
                resized_frame[:min_h, :min_w] = gray_frame[:min_h, :min_w]
                processed_frame = resized_frame
            else:
                processed_frame = gray_frame
            
            processed_frame = np.expand_dims(processed_frame, axis=0) # Add channel dimension
        else: # Should not happen if render_mode is rgb_array and env is active
            processed_frame = np.zeros((self.channels_per_frame, self.screen_height, self.screen_width), dtype=np.uint8)


        # 更新帧堆栈
        if self.frame_stack > 1:
            self.stacked_frames = np.roll(self.stacked_frames, shift=-self.channels_per_frame, axis=0)
            self.stacked_frames[-(self.channels_per_frame):, :, :] = processed_frame
        else:
            self.stacked_frames = processed_frame
            
        return self.stacked_frames.copy()

    def reset(self, seed=None, options=None):
        super().reset(seed=seed)
        _, info = self.env.reset(seed=seed, options=options)
        
        # Reset stacked_frames with the initial observation
        self.stacked_frames = np.zeros(self.observation_shape, dtype=np.uint8)
        for _ in range(self.frame_stack):
            obs_part = self._get_observation() # This will populate one frame into stacked_frames
                                               # and roll others if frame_stack > 1
                                               # The logic in _get_observation handles the stacking.
                                               # For reset, we effectively call it frame_stack times
                                               # to fill the buffer.
        return self.stacked_frames.copy(), info

    def step(self, action):
        next_state, reward, terminated, truncated, info = self.env.step(action)
        observation = self._get_observation()
        done = terminated or truncated
        return observation, reward, done, info # Gymnasium new API returns 4 values (no truncated separately in main tuple)

    def render(self):
        # Since we are getting rgb_array from self.env.render(),
        # we can return that if needed, or use a separate viewer for human display.
        # For now, let's assume the main render is for getting the array.
        # If a human render mode is added, this would handle it.
        return self.env.render() # Returns the rgb_array

    def close(self):
        self.env.close()
        if self.viewer:
            self.viewer.close()
            self.viewer = None

    def seed(self, seed=None):
        # 兼容SB3和gym旧接口
        if hasattr(super(), 'seed'):
            return super().seed(seed)
        np.random.seed(seed)
        if hasattr(self.action_space, 'seed'):
            self.action_space.seed(seed)
        if hasattr(self.observation_space, 'seed'):
            self.observation_space.seed(seed)
        self._np_random, seed = gym.utils.seeding.np_random(seed)
        return [seed]

if __name__ == '__main__':
    # Example Usage:
    env = CustomCartpoleImgEnv(frame_stack=2, screen_width=84, screen_height=84)
    print("Action Space:", env.action_space)
    print("Observation Space Shape:", env.observation_space.shape)

    obs, info = env.reset()
    print("Initial Observation Shape:", obs.shape)

    for i in range(10):
        action = env.action_space.sample()
        obs, reward, done, info = env.step(action)
        print(f"Step {i+1}: Obs Shape: {obs.shape}, Reward: {reward}, Done: {done}")
        if done:
            print("Episode finished.")
            obs, info = env.reset()
    env.close()