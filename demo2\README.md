# PPO + 编码器训练实现

本目录包含了集成编码器训练的 PPO (Proximal Policy Optimization) 的 PyTorch 实现，替代了基于 stable_baselines3 的实现。

## 概述

该实现遵循重构文档中概述的设计原则：

1. **统一训练循环**: PPO 和编码器训练集成到单一优化步骤中
2. **模块化设计**: 清晰的职责分离，专门的模块
3. **无外部依赖**: 仅使用 PyTorch 和 Gym，无需 stable_baselines3
4. **保留功能**: 所有原始功能包括图像编码、量化和重构

## 架构

### 核心组件

1. **ActorCriticNetwork** (`networks/actor_critic.py`)
   - 带共享编码器的统一网络
   - 集成图像编码、量化和策略/价值输出头
   - 支持重构以训练编码器

2. **PPOTrainer** (`ppo_trainer.py`)
   - 纯 PyTorch PPO 实现
   - 集成损失函数结合:
     - PPO 策略损失
     - 价值函数损失
     - 熵正则化
     - 重构损失
     - 量化器熵损失

3. **RolloutBuffer** (`rollout_buffer.py`)
   - 高效的数据收集和处理
   - GAE (广义优势估计) 计算
   - 训练用小批次采样

### 模型架构

```
输入图像 (64x64) 
    ↓
DTJSCC_CIFAR10_Encoder (带全局平均池化)
    ↓
GumbelSoftmaxQuantizer (16个嵌入)
    ↓
[策略头] → 动作 Logits
[价值头] → 状态价值
[解码器] → 重构图像 (用于损失)
```

## 主要特性

### 集成损失函数

总损失结合多个目标：

```python
total_loss = (
    policy_loss +                    # PPO 裁剪目标
    value_coef * value_loss +        # 价值函数 MSE
    entropy_coef * entropy_loss +    # 策略熵正则化
    recon_coef * recon_loss +        # 图像重构 MSE
    quant_entropy_coef * quant_entropy_loss  # 量化器熵正则化
)
```

### 保留原始特性

- **图像编码**: 使用原始的 DTJSCC_CIFAR10_Encoder/Decoder
- **向量量化**: 带可配置码本的 Gumbel-Softmax 量化
- **QAM 信道**: 可选的 PSK/QAM 调制和解调
- **重构训练**: 通过重构损失维持编码器质量

## 使用方法

### 快速测试

```bash
python tests/test_implementation.py
```

### 快速训练测试

```bash
python tests/quick_train_test.py
```

### 完整训练

```bash
python train.py
```

## 配置

`train.py` 中的关键超参数：

```python
config = {
    # 环境
    'img_height': 64,
    'img_width': 64,
    'use_grayscale': True,
    
    # 模型
    'latent_dim': 64,
    'num_embeddings': 16,
    'gumbel_tau': 1.0,
    
    # PPO
    'learning_rate': 3e-4,
    'n_steps': 1024,
    'batch_size': 64,
    'n_epochs': 10,
    'clip_range': 0.2,
    
    # 损失系数
    'entropy_coef': 0.01,
    'value_coef': 0.5,
    'recon_coef': 1.0,
    'quant_entropy_coef': 0.001,
}
```

## 相比 stable_baselines3 版本的优势

1. **透明性**: 所有训练逻辑都是显式和可定制的
2. **统一优化**: 所有损失的单次反向传播
3. **简化依赖**: 无需 stable_baselines3
4. **更好的集成**: 编码器训练自然集成，不是通过回调添加
5. **更易调试**: 直接访问所有中间值和梯度

## 文件结构

```
demo2/
├── networks/
│   ├── __init__.py
│   ├── actor_critic.py      # 统一的 Actor-Critic 网络
│   └── encoder_decoder.py   # 现有编码器的包装模块
├── tests/
│   ├── __init__.py
│   ├── test_implementation.py    # 单元测试
│   └── quick_train_test.py       # 快速训练测试
├── ppo_trainer.py           # PPO 训练逻辑
├── rollout_buffer.py        # 数据收集和处理
├── train.py                 # 主训练脚本
└── README.md                # 本文件
```

## 测试结果

实现已经过测试和验证：

- ✅ 模型创建和前向传播
- ✅ PPO 损失计算
- ✅ 轨迹缓冲区功能
- ✅ 环境交互
- ✅ 训练循环执行
- ✅ 图像重构

示例训练输出：
```
更新 1 | 步骤 32 | 平均奖励: 30.00
  策略损失: -0.0000 
  价值损失: 48.0652  
  重构损失: 0.2778   
  量化熵: 2.7723
  总损失: 24.3062
```

## 从之前实现的记忆

基于您的偏好：
- 损失函数使用评估网络输出 V 加上 torch.mean(loss_entr)
- 从回调中移除了奖励/观测获取和图像重构组件
- Dist 张量通过特征提取器的 dist 列表正确管理

PyTorch 实现在提供更清洁、更集成的训练方法的同时保持了这些设计选择。
