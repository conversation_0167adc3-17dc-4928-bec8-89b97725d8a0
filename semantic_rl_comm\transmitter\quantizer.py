import numpy as np
import torch
import torch.nn as nn
import torch.nn.functional as F


def _entr(dist):
    """
    Calculates the entropy of a probability distribution.
    Args:
        dist (torch.Tensor): Probability distribution. Shape: [batch_size, K]
    Returns:
        torch.Tensor: Entropy for each sample in the batch. Shape: [batch_size]
    """
    dist = dist + 1e-7  # for numerical stability
    entropy_matrix = torch.mul(-1 * dist, torch.log(dist))  # element-wise
    entropy_per_sample = torch.sum(entropy_matrix, dim=-1)  # sum over K
    return entropy_per_sample


class GumbelSoftmaxQuantizer(nn.Module):
    def __init__(self, input_feature_dim, num_embeddings_K, embedding_dim_D, gumbel_tau=1.0):
        """
        Args:
            input_feature_dim (int): Dimension of the input feature z_raw (output of ImageCNNEncoder).
            num_embeddings_K (int): Number of embeddings in the codebook (K).
            embedding_dim_D (int): Dimension of each embedding vector in the codebook (D_codebook).
            gumbel_tau (float): Temperature for Gumbel-Softmax.
        """
        super().__init__()
        self.input_feature_dim = input_feature_dim
        self.num_embeddings_K = num_embeddings_K
        self.embedding_dim_D = embedding_dim_D
        # Codebook (learnable embeddings)
        self.codebook = nn.Parameter(torch.Tensor(num_embeddings_K, embedding_dim_D))
        nn.init.uniform_(self.codebook, -1 / num_embeddings_K, 1 / num_embeddings_K)

    def compute_score(self, X, codebook_mask):
        score = torch.matmul(X, self.embedding.transpose(1, 0)) / np.sqrt(self.dim_dic)
        if codebook_mask != None:
            return score[:, codebook_mask]
        else:
            return score

    def compute_distance(self, X):
        m = torch.sum(self.embedding**2, dim=1).unsqueeze(0) + torch.sum(X**2, dim=1, keepdim=True)
        return -torch.addmm(m, X, self.embedding.transpose(1, 0), alpha=-2.0, beta=1.0)

    def sample(self, score, codebook_mask, mod=None):
        dist = F.softmax(score, dim=-1)
        if self.training:
            samples = F.gumbel_softmax(score, tau=0.5, hard=True)  # one_not
            noise = self.construct_noise(mod, codebook_mask, samples)
            samples = samples + noise
        else:
            samples = torch.argmax(score, dim=-1)
            samples = self.mod_channel_demod(mod, samples)
            samples = F.one_hot(samples, num_classes=self.top_k).float()
        return samples, dist

    def mod_channel_demod(self, mod, x):
        X = mod.modulate(x)
        X = mod.pass_channel(X)
        return mod.demodulate(X).to(self.embedding.device)

    def construct_noise(self, mod, codebook_mask, samples):
        x = torch.argmax(samples, dim=-1)
        x_tilde = self.mod_channel_demod(mod, x)
        if codebook_mask != None:
            noise = F.one_hot(x_tilde, num_classes=self.top_k).float() - F.one_hot(x, num_classes=self.top_k).float()
        else:
            noise = (
                F.one_hot(x_tilde, num_classes=self.num_embeddings).float()
                - F.one_hot(x, num_classes=self.num_embeddings).float()
            )
        return noise

    def recover(self, samples, codebook_mask):
        out = torch.matmul(samples, self.embedding[codebook_mask])
        return out

    def forward(self, X, codebook_mask, mod=None):
        score = self.compute_score(X, codebook_mask)
        samples, dist = self.sample(score, codebook_mask, mod=mod)
        out = self.recover(samples, codebook_mask)
        return out, dist
