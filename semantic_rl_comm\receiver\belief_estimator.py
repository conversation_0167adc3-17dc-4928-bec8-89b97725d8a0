import torch
import torch.nn as nn

class BeliefEstimatorRNN(nn.Module):
    def __init__(self, input_dim_Y, rnn_hidden_dim, belief_dim=4, rnn_type='lstm', num_rnn_layers=1, device='cpu'):
        """
        Belief Estimator using an RNN (LSTM or GRU).
        Args:
            input_dim_Y (int): Dimension of the input Y_t (受损码本向量, D_codebook).
            rnn_hidden_dim (int): Hidden dimension of the RNN (H_rnn).
            belief_dim (int): Dimension of the output belief state b_t (e.g., 4 for CartPole).
            rnn_type (str): Type of RNN to use, 'lstm' or 'gru'.
            num_rnn_layers (int): Number of recurrent layers.
            device (str): Device for computations.
        """
        super().__init__()
        self.input_dim_Y = input_dim_Y
        self.rnn_hidden_dim = rnn_hidden_dim
        self.belief_dim = belief_dim
        self.rnn_type = rnn_type.lower()
        self.num_rnn_layers = num_rnn_layers
        self.device = device

        if self.rnn_type == 'lstm':
            self.rnn = nn.LSTM(
                input_size=input_dim_Y,
                hidden_size=rnn_hidden_dim,
                num_layers=num_rnn_layers,
                batch_first=True  # Input/output tensors are (batch, seq, feature)
            )
        elif self.rnn_type == 'gru':
            self.rnn = nn.GRU(
                input_size=input_dim_Y,
                hidden_size=rnn_hidden_dim,
                num_layers=num_rnn_layers,
                batch_first=True
            )
        else:
            raise ValueError(f"Unsupported RNN type: {rnn_type}. Choose 'lstm' or 'gru'.")

        # Fully connected layer to map RNN output to belief state
        self.fc_belief = nn.Linear(rnn_hidden_dim, belief_dim)

        self.to(device)

    def forward(self, Y_t, h_prev=None, c_prev=None):
        """
        Processes a batch of Y_t (current time step's received vector) and previous hidden state.
        Args:
            Y_t (torch.Tensor): Batch of received (possibly noisy) codebook vectors at current time t.
                                Shape: [batch_size, input_dim_Y].
            h_prev (torch.Tensor, optional): Previous hidden state for GRU or LSTM. 
                                             Shape: [num_rnn_layers, batch_size, rnn_hidden_dim].
            c_prev (torch.Tensor, optional): Previous cell state for LSTM.
                                             Shape: [num_rnn_layers, batch_size, rnn_hidden_dim].
        Returns:
            belief_state (torch.Tensor): Batch of estimated belief states b_t.
                                         Shape: [batch_size, belief_dim].
            h_next (torch.Tensor): Next hidden state.
                                   Shape: [num_rnn_layers, batch_size, rnn_hidden_dim].
            c_next (torch.Tensor, optional): Next cell state (only for LSTM).
                                             Shape: [num_rnn_layers, batch_size, rnn_hidden_dim].
        """
        # RNN expects input of shape (batch, seq_len, feature_dim)
        # Since we process one time step at a time, seq_len = 1
        Y_t_reshaped = Y_t.unsqueeze(1)  # Shape: [batch_size, 1, input_dim_Y]

        if self.rnn_type == 'lstm':
            if h_prev is None or c_prev is None: # Initialize if not provided
                h_prev, c_prev = self.init_hidden(Y_t.size(0))
            rnn_out, (h_next, c_next) = self.rnn(Y_t_reshaped, (h_prev, c_prev))
        elif self.rnn_type == 'gru':
            if h_prev is None: # Initialize if not provided
                h_prev = self.init_hidden(Y_t.size(0))[0] # GRU only has h_prev
            rnn_out, h_next = self.rnn(Y_t_reshaped, h_prev)
            c_next = None # GRU doesn't have cell state
        else: # Should not happen due to __init__ check
            raise ValueError(f"Unsupported RNN type: {self.rnn_type}")

        # rnn_out has shape [batch_size, seq_len=1, rnn_hidden_dim]
        # We take the output of the last time step (which is the only time step here)
        rnn_out_last_step = rnn_out.squeeze(1) # Shape: [batch_size, rnn_hidden_dim]

        belief_state = self.fc_belief(rnn_out_last_step) # Shape: [batch_size, belief_dim]

        return belief_state, h_next, c_next

    def init_hidden(self, batch_size):
        """
        Initializes hidden state (and cell state for LSTM).
        Args:
            batch_size (int): The batch size.
        Returns:
            Tuple[torch.Tensor, torch.Tensor or None]: 
                h_0 (torch.Tensor): Initial hidden state. Shape [num_rnn_layers, batch_size, rnn_hidden_dim].
                c_0 (torch.Tensor or None): Initial cell state for LSTM, None for GRU. 
                                            Shape [num_rnn_layers, batch_size, rnn_hidden_dim].
        """
        h_0 = torch.zeros(self.num_rnn_layers, batch_size, self.rnn_hidden_dim, device=self.device)
        if self.rnn_type == 'lstm':
            c_0 = torch.zeros(self.num_rnn_layers, batch_size, self.rnn_hidden_dim, device=self.device)
            return h_0, c_0
        elif self.rnn_type == 'gru':
            return h_0, None # GRU only returns h_0 in a tuple for consistency if needed, or just h_0
        return h_0, None # Default case, though __init__ should prevent other rnn_types

if __name__ == '__main__':
    # Example Usage
    device_example = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    bs = 4
    d_codebook_example = 64  # input_dim_Y
    h_rnn_example = 128      # rnn_hidden_dim
    belief_d_example = 4     # belief_dim (e.g. CartPole state)
    n_layers = 1

    # Test LSTM
    print("--- Testing LSTM Belief Estimator ---")
    belief_lstm = BeliefEstimatorRNN(
        input_dim_Y=d_codebook_example,
        rnn_hidden_dim=h_rnn_example,
        belief_dim=belief_d_example,
        rnn_type='lstm',
        num_rnn_layers=n_layers,
        device=device_example
    )
    print(belief_lstm)
    dummy_Y = torch.randn(bs, d_codebook_example).to(device_example)
    h_init_lstm, c_init_lstm = belief_lstm.init_hidden(bs)
    
    b_out_lstm, h_next_lstm, c_next_lstm = belief_lstm(dummy_Y, h_init_lstm, c_init_lstm)
    print(f"Input Y_t shape: {dummy_Y.shape}")
    print(f"Belief output shape (LSTM): {b_out_lstm.shape}") # Expected: [bs, belief_d_example]
    print(f"Next hidden state h shape (LSTM): {h_next_lstm.shape}") # Expected: [n_layers, bs, h_rnn_example]
    print(f"Next cell state c shape (LSTM): {c_next_lstm.shape}")   # Expected: [n_layers, bs, h_rnn_example]
    assert b_out_lstm.shape == (bs, belief_d_example)
    assert h_next_lstm.shape == (n_layers, bs, h_rnn_example)
    assert c_next_lstm.shape == (n_layers, bs, h_rnn_example)

    # Test GRU
    print("\n--- Testing GRU Belief Estimator ---")
    belief_gru = BeliefEstimatorRNN(
        input_dim_Y=d_codebook_example,
        rnn_hidden_dim=h_rnn_example,
        belief_dim=belief_d_example,
        rnn_type='gru',
        num_rnn_layers=n_layers,
        device=device_example
    )
    print(belief_gru)
    h_init_gru, _ = belief_gru.init_hidden(bs) # GRU returns (h, None) from our init_hidden

    b_out_gru, h_next_gru, c_next_gru = belief_gru(dummy_Y, h_init_gru) # c_prev is not used for GRU
    print(f"Input Y_t shape: {dummy_Y.shape}")
    print(f"Belief output shape (GRU): {b_out_gru.shape}") # Expected: [bs, belief_d_example]
    print(f"Next hidden state h shape (GRU): {h_next_gru.shape}") # Expected: [n_layers, bs, h_rnn_example]
    print(f"Next cell state c shape (GRU): {c_next_gru}")   # Expected: None
    assert b_out_gru.shape == (bs, belief_d_example)
    assert h_next_gru.shape == (n_layers, bs, h_rnn_example)
    assert c_next_gru is None